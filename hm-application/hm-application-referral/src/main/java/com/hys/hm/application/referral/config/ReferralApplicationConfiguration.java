package com.hys.hm.application.referral.config;

import com.hys.hm.domain.referral.repository.ReferralRepository;
import com.hys.hm.domain.referral.service.ReferralDomainService;
import com.hys.hm.domain.referral.service.ReferralQueryService;
import com.hys.hm.infrastructure.persistence.referral.repository.ReferralRepositoryImpl;
import com.hys.hm.infrastructure.persistence.referral.service.ReferralQueryServiceImpl;
import com.hys.hm.shared.events.EventPublisher;
import com.hys.hm.shared.framework.repository.BaseRepositoryFactoryBean;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

/**
 * 转诊应用服务配置类
 * 配置转诊模块的所有Bean和依赖关系
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-29
 */
@Configuration
@ComponentScan(basePackages = {
    "com.hys.hm.domain.referral",
    "com.hys.hm.application.referral",
    "com.hys.hm.infrastructure.persistence.referral"
})
@EnableJpaRepositories(
    basePackages = "com.hys.hm.infrastructure.persistence.referral.repository",
    repositoryFactoryBeanClass = BaseRepositoryFactoryBean.class
)
@EntityScan(basePackages = "com.hys.hm.infrastructure.persistence.referral.entity")
public class ReferralApplicationConfiguration {
    
    /**
     * 转诊仓储Bean
     * 将基础设施层的实现注入到领域层接口
     */
    @Bean
    @Primary
    public ReferralRepository referralRepository(ReferralRepositoryImpl referralRepositoryImpl) {
        return referralRepositoryImpl;
    }
    
    /**
     * 转诊查询服务Bean
     * 将基础设施层的实现注入到领域层接口
     */
    @Bean
    @Primary
    public ReferralQueryService referralQueryService(ReferralQueryServiceImpl referralQueryServiceImpl) {
        return referralQueryServiceImpl;
    }
    
    /**
     * 转诊领域服务Bean
     * 注入依赖的仓储和事件发布器
     */
    @Bean
    public ReferralDomainService referralDomainService(
            ReferralRepository referralRepository,
            EventPublisher eventPublisher) {
        return new ReferralDomainService(referralRepository, eventPublisher);
    }
}
