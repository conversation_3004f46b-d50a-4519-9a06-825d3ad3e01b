package com.hys.hm.application.referral.converter;

import com.hys.hm.application.referral.dto.ReferralCreateDTO;
import com.hys.hm.application.referral.dto.ReferralQueryDTO;
import com.hys.hm.domain.referral.model.HospitalInfo;
import com.hys.hm.domain.referral.model.MedicalInfo;
import com.hys.hm.domain.referral.model.PatientInfo;
import com.hys.hm.domain.referral.model.ReferralForm;
import com.hys.hm.shared.types.enums.ReferralStatus;
import com.hys.hm.shared.types.enums.UrgencyLevel;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * 转诊DTO转换器
 * 负责应用层DTO与领域模型之间的转换
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-29
 */
@Component
public class ReferralDTOConverter {
    
    /**
     * 创建DTO转换为领域模型
     */
    public ReferralForm toReferralForm(ReferralCreateDTO createDTO) {
        if (createDTO == null) {
            return null;
        }
        
        // 构建患者信息
        PatientInfo patientInfo = PatientInfo.builder()
                .patientId(createDTO.getBasicInfoId())
                .name(createDTO.getPatientName())
                .gender(createDTO.getGender())
                .age(createDTO.getAge())
                .idCard(createDTO.getIdCard())
                .phone(createDTO.getPhone())
                .address(createDTO.getAddress())
                .addressDetail(createDTO.getAddressDetail())
                .fileNumber(createDTO.getFileNumber())
                .build();
        
        // 构建医疗信息
        MedicalInfo medicalInfo = MedicalInfo.builder()
                .mainSymptoms(createDTO.getMainSymptoms())
                .preliminaryDiagnosis(createDTO.getPreliminaryDiagnosis())
                .medicalHistory(createDTO.getMedicalHistory())
                .examResults(createDTO.getExamResults())
                .treatmentHistory(createDTO.getTreatmentHistory())
                .referralReason(createDTO.getReferralReason())
                .referralPurpose(createDTO.getReferralPurpose())
                .build();
        
        // 构建转出医院信息
        HospitalInfo outHospital = HospitalInfo.builder()
                .unitId(createDTO.getOutUnitId())
                .unitName(createDTO.getOutUnitName())
                .deptId(createDTO.getOutDeptId())
                .deptName(createDTO.getOutDeptName())
                .doctorId(createDTO.getOutDoctorId())
                .doctorName(createDTO.getOutDoctorName())
                .doctorPhone(createDTO.getOutDoctorPhone())
                .build();
        
        // 构建转入医院信息
        HospitalInfo inHospital = HospitalInfo.builder()
                .unitId(createDTO.getInUnitId())
                .unitName(createDTO.getInUnitName())
                .deptId(createDTO.getInDeptId())
                .deptName(createDTO.getInDeptName())
                .doctorId(createDTO.getInDoctorId())
                .doctorName(createDTO.getInDoctorName())
                .doctorPhone(createDTO.getInDoctorPhone())
                .build();
        
        // 构建转诊表单
        return ReferralForm.builder()
                .basicInfoId(createDTO.getBasicInfoId())
                .referralDate(createDTO.getReferralDate() != null ? createDTO.getReferralDate() : LocalDateTime.now())
                .patientInfo(patientInfo)
                .medicalInfo(medicalInfo)
                .outHospital(outHospital)
                .inHospital(inHospital)
                .urgencyLevel(createDTO.getUrgencyLevel())
                .appointmentTime(createDTO.getAppointmentTime())
                .attachments(createDTO.getAttachments())
                .notes(createDTO.getNotes())
                .build();
    }
    
    /**
     * 领域模型转换为查询DTO
     */
    public ReferralQueryDTO toQueryDTO(ReferralForm referralForm) {
        if (referralForm == null) {
            return null;
        }
        
        ReferralQueryDTO queryDTO = new ReferralQueryDTO();
        queryDTO.setId(referralForm.getId());
        queryDTO.setBasicInfoId(referralForm.getBasicInfoId());
        queryDTO.setReferralNo(referralForm.getReferralNo());
        queryDTO.setReferralDate(referralForm.getReferralDate());
        queryDTO.setStatus(referralForm.getStatus());
        queryDTO.setRejectReason(referralForm.getRejectReason());
        queryDTO.setConfirmTime(referralForm.getConfirmTime());
        queryDTO.setUrgencyLevel(referralForm.getUrgencyLevel());
        queryDTO.setAppointmentTime(referralForm.getAppointmentTime());
        queryDTO.setAttachments(referralForm.getAttachments());
        queryDTO.setNotes(referralForm.getNotes());
        queryDTO.setCreateTime(referralForm.getCreateTime());
        queryDTO.setUpdateTime(referralForm.getUpdateTime());
        queryDTO.setCreateBy(referralForm.getCreateBy());
        queryDTO.setUpdateBy(referralForm.getUpdateBy());
        queryDTO.setVersion(referralForm.getVersion());
        
        // 患者信息
        if (referralForm.getPatientInfo() != null) {
            PatientInfo patientInfo = referralForm.getPatientInfo();
            queryDTO.setPatientName(patientInfo.getName());
            queryDTO.setGender(patientInfo.getGender());
            queryDTO.setAge(patientInfo.getAge());
            queryDTO.setIdCard(maskIdCard(patientInfo.getIdCard()));
            queryDTO.setPhone(maskPhone(patientInfo.getPhone()));
            queryDTO.setAddress(patientInfo.getAddress());
            queryDTO.setAddressDetail(patientInfo.getAddressDetail());
            queryDTO.setFileNumber(patientInfo.getFileNumber());
        }
        
        // 医疗信息
        if (referralForm.getMedicalInfo() != null) {
            MedicalInfo medicalInfo = referralForm.getMedicalInfo();
            queryDTO.setMainSymptoms(medicalInfo.getMainSymptoms());
            queryDTO.setPreliminaryDiagnosis(medicalInfo.getPreliminaryDiagnosis());
            queryDTO.setMedicalHistory(medicalInfo.getMedicalHistory());
            queryDTO.setExamResults(medicalInfo.getExamResults());
            queryDTO.setTreatmentHistory(medicalInfo.getTreatmentHistory());
            queryDTO.setReferralReason(medicalInfo.getReferralReason());
            queryDTO.setReferralPurpose(medicalInfo.getReferralPurpose());
        }
        
        // 转出医院信息
        if (referralForm.getOutHospital() != null) {
            HospitalInfo outHospital = referralForm.getOutHospital();
            queryDTO.setOutUnitId(outHospital.getUnitId());
            queryDTO.setOutUnitName(outHospital.getUnitName());
            queryDTO.setOutDeptId(outHospital.getDeptId());
            queryDTO.setOutDeptName(outHospital.getDeptName());
            queryDTO.setOutDoctorId(outHospital.getDoctorId());
            queryDTO.setOutDoctorName(outHospital.getDoctorName());
            queryDTO.setOutDoctorPhone(outHospital.getDoctorPhone());
        }
        
        // 转入医院信息
        if (referralForm.getInHospital() != null) {
            HospitalInfo inHospital = referralForm.getInHospital();
            queryDTO.setInUnitId(inHospital.getUnitId());
            queryDTO.setInUnitName(inHospital.getUnitName());
            queryDTO.setInDeptId(inHospital.getDeptId());
            queryDTO.setInDeptName(inHospital.getDeptName());
            queryDTO.setInDoctorId(inHospital.getDoctorId());
            queryDTO.setInDoctorName(inHospital.getDoctorName());
            queryDTO.setInDoctorPhone(inHospital.getDoctorPhone());
        }

        return queryDTO;
    }
    
    /**
     * 脱敏身份证号
     */
    private String maskIdCard(String idCard) {
        if (idCard == null || idCard.length() < 8) {
            return idCard;
        }
        return idCard.substring(0, 4) + "**********" + idCard.substring(idCard.length() - 4);
    }
    
    /**
     * 脱敏手机号
     */
    private String maskPhone(String phone) {
        if (phone == null || phone.length() < 7) {
            return phone;
        }

        // 对于11位手机号（标准格式），显示前3位和后4位
        if (phone.length() == 11) {
            return phone.substring(0, 3) + "****" + phone.substring(7);
        }

        // 对于其他长度的手机号，显示前3位，其余用星号遮挡
        if (phone.length() <= 7) {
            return phone.substring(0, 3) + "****";
        } else {
            // 长度大于7但不等于11的情况，显示前3位和后面部分
            return phone.substring(0, 3) + "****" + phone.substring(phone.length() - 4);
        }
    }
}
