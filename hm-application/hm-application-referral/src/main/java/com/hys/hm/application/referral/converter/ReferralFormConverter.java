package com.hys.hm.application.referral.converter;

import com.hys.hm.application.referral.dto.ReferralFormCreateDTO;
import com.hys.hm.application.referral.dto.ReferralFormQueryDTO;
import com.hys.hm.domain.referral.model.ReferralForm;
import com.hys.hm.domain.referral.enums.Gender;
import com.hys.hm.domain.referral.valueobject.PatientInfo;
import com.hys.hm.domain.referral.valueobject.MedicalInfo;
import com.hys.hm.domain.referral.valueobject.HospitalInfo;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 转诊表单转换器
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-23
 */
@Component
public class ReferralFormConverter {
    
    /**
     * 创建DTO转换为值对象
     */
    public PatientInfo toPatientInfo(ReferralFormCreateDTO createDTO) {
        PatientInfo patientInfo = PatientInfo.create(
            createDTO.getPatientName(),
            Gender.fromCode(createDTO.getGender()),
            createDTO.getAge(),
            createDTO.getIdCard(),
            createDTO.getPhone()
        );
        
        patientInfo.setAddress(createDTO.getAddress());
        patientInfo.setAddressDetail(createDTO.getAddressDetail());
        patientInfo.setFileNumber(createDTO.getFileNumber());
        
        return patientInfo;
    }
    
    /**
     * 创建DTO转换为医疗信息
     */
    public MedicalInfo toMedicalInfo(ReferralFormCreateDTO createDTO) {
        MedicalInfo medicalInfo = MedicalInfo.create(
            createDTO.getImpression(),
            createDTO.getMainHistory()
        );
        
        medicalInfo.setPastHistory(createDTO.getPastHistory());
        medicalInfo.setTreatmentProcess(createDTO.getTreatmentProcess());
        
        return medicalInfo;
    }
    
    /**
     * 创建DTO转换为转出医院信息
     */
    public HospitalInfo toOutHospitalInfo(ReferralFormCreateDTO createDTO) {
        HospitalInfo hospitalInfo = HospitalInfo.create(
            createDTO.getOutUnitId(),
            createDTO.getOutUnitName(),
            createDTO.getOutDoctorId(),
            createDTO.getOutDoctorName()
        );
        
        hospitalInfo.setDeptId(createDTO.getOutDeptId());
        hospitalInfo.setDeptName(createDTO.getOutDeptName());
        hospitalInfo.setDoctorPhone(createDTO.getOutDoctorPhone());
        
        return hospitalInfo;
    }
    
    /**
     * 创建DTO转换为转入医院信息
     */
    public HospitalInfo toInHospitalInfo(ReferralFormCreateDTO createDTO) {
        HospitalInfo hospitalInfo = HospitalInfo.create(
            createDTO.getInUnitId(),
            createDTO.getInUnitName(),
            createDTO.getInDoctorId(),
            createDTO.getInDoctorName()
        );
        
        hospitalInfo.setDeptId(createDTO.getInDeptId());
        hospitalInfo.setDeptName(createDTO.getInDeptName());
        hospitalInfo.setDoctorPhone(createDTO.getInDoctorPhone());
        
        return hospitalInfo;
    }
    
    /**
     * 领域模型转换为查询DTO
     */
    public ReferralFormQueryDTO toQueryDTO(ReferralForm referralForm) {
        if (referralForm == null) {
            return null;
        }
        
        ReferralFormQueryDTO queryDTO = new ReferralFormQueryDTO();
        
        // 基本信息
        queryDTO.setId(referralForm.getId());
        queryDTO.setBasicInfoId(referralForm.getBasicInfoId());
        queryDTO.setReferralNo(referralForm.getReferralNo());
        queryDTO.setReferralDate(referralForm.getReferralDate());
        queryDTO.setCreateTime(referralForm.getCreateTime());
        
        // 患者信息
        if (referralForm.getPatientInfo() != null) {
            PatientInfo patientInfo = referralForm.getPatientInfo();
            queryDTO.setPatientName(patientInfo.getName());
            queryDTO.setGender(patientInfo.getGender().getCode());
            queryDTO.setGenderDesc(patientInfo.getGender().getDescription());
            queryDTO.setAge(patientInfo.getAge());
            queryDTO.setMaskedIdCard(patientInfo.getMaskedIdCard());
            queryDTO.setMaskedPhone(patientInfo.getMaskedPhone());
            queryDTO.setFullAddress(patientInfo.getFullAddress());
            queryDTO.setFileNumber(patientInfo.getFileNumber());
        }
        
        // 医疗信息
        if (referralForm.getMedicalInfo() != null) {
            MedicalInfo medicalInfo = referralForm.getMedicalInfo();
            queryDTO.setImpression(medicalInfo.getImpression());
            queryDTO.setHistorySummary(medicalInfo.getHistorySummary());
            queryDTO.setHasTreatmentRecord(medicalInfo.hasTreatmentRecord());
        }
        
        // 转出医院信息
        if (referralForm.getOutHospitalInfo() != null) {
            HospitalInfo outHospitalInfo = referralForm.getOutHospitalInfo();
            queryDTO.setOutUnitId(outHospitalInfo.getUnitId());
            queryDTO.setOutHospitalInfo(outHospitalInfo.getFullHospitalInfo());
            queryDTO.setOutDoctorInfo(outHospitalInfo.getFullDoctorInfo());
        }
        
        // 转入医院信息
        if (referralForm.getInHospitalInfo() != null) {
            HospitalInfo inHospitalInfo = referralForm.getInHospitalInfo();
            queryDTO.setInUnitId(inHospitalInfo.getUnitId());
            queryDTO.setInHospitalInfo(inHospitalInfo.getFullHospitalInfo());
            queryDTO.setInDoctorInfo(inHospitalInfo.getFullDoctorInfo());
        }
        
        // 状态信息
        if (referralForm.getStatus() != null) {
            queryDTO.setStatus(referralForm.getStatus().getCode());
            queryDTO.setStatusDesc(referralForm.getStatus().getDescription());
        }
        queryDTO.setRejectReason(referralForm.getRejectReason());
        queryDTO.setConfirmTime(referralForm.getConfirmTime());
        
        // 业务状态
        queryDTO.setIsPending(referralForm.isPending());
        queryDTO.setIsConfirmed(referralForm.isConfirmed());
        queryDTO.setIsCompleted(referralForm.isCompleted());
        queryDTO.setIsRejected(referralForm.isRejected());
        queryDTO.setIsCancelled(referralForm.isCancelled());
        queryDTO.setIsTimeout(referralForm.isTimeout());
        queryDTO.setReferralDescription(referralForm.getReferralDescription());
        
        return queryDTO;
    }
    
    /**
     * 领域模型列表转换为查询DTO列表
     */
    public List<ReferralFormQueryDTO> toQueryDTOList(List<ReferralForm> referralForms) {
        if (referralForms == null) {
            return null;
        }
        
        return referralForms.stream()
                .map(this::toQueryDTO)
                .collect(Collectors.toList());
    }
}
