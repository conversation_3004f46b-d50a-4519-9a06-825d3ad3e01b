package com.hys.hm.application.referral.dto;

import com.hys.hm.shared.types.enums.UrgencyLevel;
import jakarta.validation.constraints.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 转诊创建DTO
 * 用于接收创建转诊表单的请求参数
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-29
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReferralCreateDTO {
    
    /**
     * 基础信息ID
     */
    @NotBlank(message = "基础信息ID不能为空")
    private String basicInfoId;
    
    /**
     * 转诊日期
     */
    private LocalDateTime referralDate;
    
    // ========== 患者信息 ==========
    
    /**
     * 患者姓名
     */
    @NotBlank(message = "患者姓名不能为空")
    @Size(max = 50, message = "患者姓名长度不能超过50个字符")
    private String patientName;
    
    /**
     * 性别：1-男，2-女
     */
    @NotNull(message = "性别不能为空")
    @Min(value = 1, message = "性别值必须为1或2")
    @Max(value = 2, message = "性别值必须为1或2")
    private Integer gender;
    
    /**
     * 年龄
     */
    @NotNull(message = "年龄不能为空")
    @Min(value = 0, message = "年龄不能小于0")
    @Max(value = 150, message = "年龄不能大于150")
    private Integer age;
    
    /**
     * 身份证号
     */
    @Size(max = 18, message = "身份证号长度不能超过18位")
    private String idCard;
    
    /**
     * 联系电话
     */
    @NotBlank(message = "联系电话不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "联系电话格式不正确")
    private String phone;
    
    /**
     * 地址
     */
    @Size(max = 200, message = "地址长度不能超过200个字符")
    private String address;
    
    /**
     * 详细地址
     */
    @Size(max = 200, message = "详细地址长度不能超过200个字符")
    private String addressDetail;
    
    /**
     * 档案编号
     */
    @Size(max = 50, message = "档案编号长度不能超过50个字符")
    private String fileNumber;
    
    // ========== 医疗信息 ==========
    
    /**
     * 主要症状
     */
    @Size(max = 1000, message = "主要症状长度不能超过1000个字符")
    private String mainSymptoms;
    
    /**
     * 初步诊断
     */
    @Size(max = 500, message = "初步诊断长度不能超过500个字符")
    private String preliminaryDiagnosis;
    
    /**
     * 病史摘要
     */
    @Size(max = 2000, message = "病史摘要长度不能超过2000个字符")
    private String medicalHistory;
    
    /**
     * 检查结果
     */
    @Size(max = 2000, message = "检查结果长度不能超过2000个字符")
    private String examResults;
    
    /**
     * 治疗经过
     */
    @Size(max = 2000, message = "治疗经过长度不能超过2000个字符")
    private String treatmentHistory;
    
    /**
     * 转诊原因
     */
    @NotBlank(message = "转诊原因不能为空")
    @Size(max = 1000, message = "转诊原因长度不能超过1000个字符")
    private String referralReason;
    
    /**
     * 转诊目的
     */
    @Size(max = 500, message = "转诊目的长度不能超过500个字符")
    private String referralPurpose;
    
    // ========== 转出医院信息 ==========
    
    /**
     * 转出医院ID
     */
    @NotBlank(message = "转出医院ID不能为空")
    private String outUnitId;
    
    /**
     * 转出医院名称
     */
    @NotBlank(message = "转出医院名称不能为空")
    @Size(max = 200, message = "转出医院名称长度不能超过200个字符")
    private String outUnitName;
    
    /**
     * 转出科室ID
     */
    private String outDeptId;
    
    /**
     * 转出科室名称
     */
    @Size(max = 100, message = "转出科室名称长度不能超过100个字符")
    private String outDeptName;
    
    /**
     * 转出医生ID
     */
    private String outDoctorId;
    
    /**
     * 转出医生姓名
     */
    @Size(max = 50, message = "转出医生姓名长度不能超过50个字符")
    private String outDoctorName;
    
    /**
     * 转出医生联系方式
     */
    @Size(max = 50, message = "转出医生联系方式长度不能超过50个字符")
    private String outDoctorPhone;
    
    // ========== 转入医院信息 ==========
    
    /**
     * 转入医院ID
     */
    @NotBlank(message = "转入医院ID不能为空")
    private String inUnitId;
    
    /**
     * 转入医院名称
     */
    @NotBlank(message = "转入医院名称不能为空")
    @Size(max = 200, message = "转入医院名称长度不能超过200个字符")
    private String inUnitName;
    
    /**
     * 转入科室ID
     */
    private String inDeptId;
    
    /**
     * 转入科室名称
     */
    @Size(max = 100, message = "转入科室名称长度不能超过100个字符")
    private String inDeptName;
    
    /**
     * 转入医生ID
     */
    private String inDoctorId;
    
    /**
     * 转入医生姓名
     */
    @Size(max = 50, message = "转入医生姓名长度不能超过50个字符")
    private String inDoctorName;
    
    /**
     * 转入医生联系方式
     */
    @Size(max = 50, message = "转入医生联系方式长度不能超过50个字符")
    private String inDoctorPhone;
    
    // ========== 其他信息 ==========
    
    /**
     * 紧急程度
     */
    private UrgencyLevel urgencyLevel = UrgencyLevel.NORMAL;
    
    /**
     * 预约时间
     */
    private LocalDateTime appointmentTime;
    
    /**
     * 附件信息
     */
    @Size(max = 2000, message = "附件信息长度不能超过2000个字符")
    private String attachments;
    
    /**
     * 备注信息
     */
    @Size(max = 1000, message = "备注信息长度不能超过1000个字符")
    private String notes;
}
