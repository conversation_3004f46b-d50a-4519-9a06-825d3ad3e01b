package com.hys.hm.application.referral.service;

import com.hys.hm.application.referral.dto.ReferralFormCreateDTO;
import com.hys.hm.application.referral.dto.ReferralFormQueryDTO;
import com.hys.hm.application.referral.dto.ReferralFormSearchDTO;
import com.hys.hm.application.referral.converter.ReferralFormConverter;
import com.hys.hm.domain.referral.model.ReferralForm;
import com.hys.hm.domain.referral.repository.ReferralFormRepository;
import com.hys.hm.domain.referral.enums.ReferralStatus;
import com.hys.hm.domain.referral.valueobject.PatientInfo;
import com.hys.hm.domain.referral.valueobject.MedicalInfo;
import com.hys.hm.domain.referral.valueobject.HospitalInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 转诊表单应用服务
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-23
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ReferralFormApplicationService {
    
    private final ReferralFormDomainService referralFormDomainService;
    private final ReferralFormRepository referralFormRepository;
    private final ReferralFormConverter referralFormConverter;
    
    /**
     * 创建转诊表单
     */
    @Transactional
    public ReferralFormQueryDTO createReferralForm(ReferralFormCreateDTO createDTO, String operatorId) {
        log.info("创建转诊表单，操作人: {}, 患者: {}", operatorId, createDTO.getPatientName());
        
        // 转换DTO为值对象
        PatientInfo patientInfo = referralFormConverter.toPatientInfo(createDTO);
        MedicalInfo medicalInfo = referralFormConverter.toMedicalInfo(createDTO);
        HospitalInfo outHospitalInfo = referralFormConverter.toOutHospitalInfo(createDTO);
        HospitalInfo inHospitalInfo = referralFormConverter.toInHospitalInfo(createDTO);
        
        // 创建转诊表单
        ReferralForm referralForm = referralFormDomainService.createReferralForm(
            createDTO.getBasicInfoId(), patientInfo, medicalInfo, 
            outHospitalInfo, inHospitalInfo
        );
        
        // 转换为DTO返回
        return referralFormConverter.toQueryDTO(referralForm);
    }
    
    /**
     * 确认转诊
     */
    @Transactional
    public void confirmReferral(String referralId, String operatorId) {
        log.info("确认转诊，转诊ID: {}, 操作人: {}", referralId, operatorId);
        referralFormDomainService.confirmReferral(referralId, operatorId);
    }
    
    /**
     * 拒绝转诊
     */
    @Transactional
    public void rejectReferral(String referralId, String rejectReason, String operatorId) {
        log.info("拒绝转诊，转诊ID: {}, 操作人: {}", referralId, operatorId);
        referralFormDomainService.rejectReferral(referralId, rejectReason, operatorId);
    }
    
    /**
     * 取消转诊
     */
    @Transactional
    public void cancelReferral(String referralId, String operatorId) {
        log.info("取消转诊，转诊ID: {}, 操作人: {}", referralId, operatorId);
        referralFormDomainService.cancelReferral(referralId, operatorId);
    }
    
    /**
     * 完成转诊
     */
    @Transactional
    public void completeReferral(String referralId, String operatorId) {
        log.info("完成转诊，转诊ID: {}, 操作人: {}", referralId, operatorId);
        referralFormDomainService.completeReferral(referralId, operatorId);
    }
    
    /**
     * 根据ID查询转诊表单
     */
    public Optional<ReferralFormQueryDTO> findById(String id) {
        return referralFormRepository.findById(id)
                .map(referralFormConverter::toQueryDTO);
    }
    
    /**
     * 根据转诊编号查询转诊表单
     */
    public Optional<ReferralFormQueryDTO> findByReferralNo(String referralNo) {
        return referralFormRepository.findByReferralNo(referralNo)
                .map(referralFormConverter::toQueryDTO);
    }
    
    /**
     * 根据基础信息ID查询转诊表单列表
     */
    public List<ReferralFormQueryDTO> findByBasicInfoId(String basicInfoId) {
        List<ReferralForm> referralForms = referralFormRepository.findByBasicInfoId(basicInfoId);
        return referralFormConverter.toQueryDTOList(referralForms);
    }
    
    /**
     * 根据转出医院查询转诊表单列表
     */
    public List<ReferralFormQueryDTO> findByOutHospital(String outUnitId) {
        List<ReferralForm> referralForms = referralFormRepository.findByOutHospital(outUnitId);
        return referralFormConverter.toQueryDTOList(referralForms);
    }
    
    /**
     * 根据转入医院查询转诊表单列表
     */
    public List<ReferralFormQueryDTO> findByInHospital(String inUnitId) {
        List<ReferralForm> referralForms = referralFormRepository.findByInHospital(inUnitId);
        return referralFormConverter.toQueryDTOList(referralForms);
    }
    
    /**
     * 根据转出医生查询转诊表单列表
     */
    public List<ReferralFormQueryDTO> findByOutDoctor(String outDoctorId) {
        List<ReferralForm> referralForms = referralFormRepository.findByOutDoctor(outDoctorId);
        return referralFormConverter.toQueryDTOList(referralForms);
    }
    
    /**
     * 根据转入医生查询转诊表单列表
     */
    public List<ReferralFormQueryDTO> findByInDoctor(String inDoctorId) {
        List<ReferralForm> referralForms = referralFormRepository.findByInDoctor(inDoctorId);
        return referralFormConverter.toQueryDTOList(referralForms);
    }
    
    /**
     * 根据状态查询转诊表单列表
     */
    public List<ReferralFormQueryDTO> findByStatus(Integer statusCode) {
        ReferralStatus status = ReferralStatus.fromCode(statusCode);
        List<ReferralForm> referralForms = referralFormRepository.findByStatus(status);
        return referralFormConverter.toQueryDTOList(referralForms);
    }
    
    /**
     * 根据患者姓名模糊查询转诊表单列表
     */
    public List<ReferralFormQueryDTO> findByPatientNameLike(String patientName) {
        List<ReferralForm> referralForms = referralFormRepository.findByPatientNameLike(patientName);
        return referralFormConverter.toQueryDTOList(referralForms);
    }
    
    /**
     * 根据搜索条件查询转诊表单列表
     */
    public List<ReferralFormQueryDTO> searchReferralForms(ReferralFormSearchDTO searchDTO) {
        ReferralStatus status = searchDTO.getStatus() != null ? 
                               ReferralStatus.fromCode(searchDTO.getStatus()) : null;
        
        List<ReferralForm> referralForms = referralFormRepository.findByConditionsWithPagination(
            searchDTO.getOutUnitId(),
            searchDTO.getInUnitId(),
            status,
            searchDTO.getStartDate(),
            searchDTO.getEndDate(),
            searchDTO.getPage(),
            searchDTO.getSize()
        );
        
        return referralFormConverter.toQueryDTOList(referralForms);
    }
    
    /**
     * 分页查询转诊表单列表
     */
    public List<ReferralFormQueryDTO> findWithPagination(int page, int size) {
        List<ReferralForm> referralForms = referralFormRepository.findWithPagination(page, size);
        return referralFormConverter.toQueryDTOList(referralForms);
    }
    
    /**
     * 查询超时的转诊表单
     */
    public List<ReferralFormQueryDTO> findTimeoutReferrals() {
        List<ReferralForm> referralForms = referralFormDomainService.findTimeoutReferrals();
        return referralFormConverter.toQueryDTOList(referralForms);
    }
    
    /**
     * 获取医院转诊统计
     */
    public ReferralFormDomainService.ReferralStatistics getHospitalStatistics(String unitId) {
        return referralFormDomainService.getHospitalStatistics(unitId);
    }
    
    /**
     * 统计转诊表单总数
     */
    public long count() {
        return referralFormRepository.count();
    }
    
    /**
     * 根据状态统计转诊表单数量
     */
    public long countByStatus(Integer statusCode) {
        ReferralStatus status = ReferralStatus.fromCode(statusCode);
        return referralFormRepository.countByStatus(status);
    }

    /**
     * 根据身份证号查询转诊表单（支持加密字段）
     */
    public List<ReferralFormQueryDTO> findByIdCard(String idCard, boolean exactMatch) {
        log.info("根据身份证号查询转诊表单，身份证号: {}, 精确匹配: {}", maskSensitiveData(idCard), exactMatch);

        List<ReferralForm> referralForms = referralFormRepository.findByIdCard(idCard, exactMatch);
        return referralFormConverter.toQueryDTOList(referralForms);
    }

    /**
     * 根据手机号查询转诊表单（支持加密字段）
     */
    public List<ReferralFormQueryDTO> findByPhone(String phone, boolean exactMatch) {
        log.info("根据手机号查询转诊表单，手机号: {}, 精确匹配: {}", maskSensitiveData(phone), exactMatch);

        List<ReferralForm> referralForms = referralFormRepository.findByPhone(phone, exactMatch);
        return referralFormConverter.toQueryDTOList(referralForms);
    }

    /**
     * 脱敏敏感数据用于日志显示
     */
    private String maskSensitiveData(String data) {
        if (data == null || data.length() <= 4) {
            return "****";
        }
        return data.substring(0, 3) + "****" + data.substring(data.length() - 1);
    }
}
