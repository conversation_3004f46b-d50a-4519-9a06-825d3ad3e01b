package com.hys.hm.application.referral.service;

import com.hys.hm.infrastructure.persistence.referral.entity.ReferralFormEntityNew;
import com.hys.hm.shared.framework.service.BaseService;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 转诊表单服务接口（使用新框架）
 * 继承BaseService获得完整的CRUD和业务逻辑功能
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-29
 */
public interface ReferralFormServiceNew extends BaseService<ReferralFormEntityNew, String> {

    /**
     * 根据转诊编号查找转诊表单
     * 
     * @param referralNo 转诊编号
     * @return 转诊表单（可能为空）
     */
    Optional<ReferralFormEntityNew> findByReferralNo(String referralNo);

    /**
     * 根据基础信息ID查找转诊表单列表
     * 
     * @param basicInfoId 基础信息ID
     * @return 转诊表单列表
     */
    List<ReferralFormEntityNew> findByBasicInfoId(String basicInfoId);

    /**
     * 根据转出医院查找转诊表单列表
     * 
     * @param outUnitId 转出医院ID
     * @return 转诊表单列表
     */
    List<ReferralFormEntityNew> findByOutUnitId(String outUnitId);

    /**
     * 根据转入医院查找转诊表单列表
     * 
     * @param inUnitId 转入医院ID
     * @return 转诊表单列表
     */
    List<ReferralFormEntityNew> findByInUnitId(String inUnitId);

    /**
     * 根据医生查找转诊表单列表
     * 
     * @param doctorId 医生ID（转出或转入）
     * @return 转诊表单列表
     */
    List<ReferralFormEntityNew> findByDoctorId(String doctorId);

    /**
     * 根据状态查找转诊表单列表
     * 
     * @param status 转诊状态
     * @return 转诊表单列表
     */
    List<ReferralFormEntityNew> findByStatus(Integer status);

    /**
     * 根据患者姓名模糊查找转诊表单列表
     * 
     * @param patientName 患者姓名
     * @return 转诊表单列表
     */
    List<ReferralFormEntityNew> findByPatientNameLike(String patientName);

    /**
     * 根据时间范围查找转诊表单列表
     * 
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 转诊表单列表
     */
    List<ReferralFormEntityNew> findByDateRange(LocalDateTime startDate, LocalDateTime endDate);

    /**
     * 根据身份证号查找转诊表单列表（支持加密字段）
     * 
     * @param idCard 身份证号
     * @return 转诊表单列表
     */
    List<ReferralFormEntityNew> findByIdCard(String idCard);

    /**
     * 根据手机号查找转诊表单列表（支持加密字段）
     * 
     * @param phone 手机号
     * @return 转诊表单列表
     */
    List<ReferralFormEntityNew> findByPhone(String phone);

    /**
     * 查找待处理的转诊表单
     * 
     * @return 待处理的转诊表单列表
     */
    List<ReferralFormEntityNew> findPendingReferrals();

    /**
     * 查找紧急转诊表单
     * 
     * @return 紧急转诊表单列表
     */
    List<ReferralFormEntityNew> findUrgentReferrals();

    /**
     * 查找超时的转诊表单
     * 
     * @param timeoutDays 超时天数
     * @return 超时的转诊表单列表
     */
    List<ReferralFormEntityNew> findTimeoutReferrals(int timeoutDays);

    /**
     * 查找今日转诊表单
     * 
     * @return 今日转诊表单列表
     */
    List<ReferralFormEntityNew> findTodayReferrals();

    /**
     * 确认转诊
     * 
     * @param id 转诊表单ID
     * @param operatorId 操作人ID
     * @return 是否成功
     */
    boolean confirmReferral(String id, String operatorId);

    /**
     * 拒绝转诊
     * 
     * @param id 转诊表单ID
     * @param rejectReason 拒绝原因
     * @param operatorId 操作人ID
     * @return 是否成功
     */
    boolean rejectReferral(String id, String rejectReason, String operatorId);

    /**
     * 取消转诊
     * 
     * @param id 转诊表单ID
     * @param operatorId 操作人ID
     * @return 是否成功
     */
    boolean cancelReferral(String id, String operatorId);

    /**
     * 完成转诊
     * 
     * @param id 转诊表单ID
     * @param operatorId 操作人ID
     * @return 是否成功
     */
    boolean completeReferral(String id, String operatorId);

    /**
     * 生成转诊编号
     * 
     * @return 转诊编号
     */
    String generateReferralNo();

    /**
     * 检查转诊编号是否存在
     * 
     * @param referralNo 转诊编号
     * @return 是否存在
     */
    boolean isReferralNoExists(String referralNo);

    /**
     * 根据状态统计转诊表单数量
     * 
     * @param status 转诊状态
     * @return 数量
     */
    long countByStatus(Integer status);

    /**
     * 根据医院统计转诊表单数量
     * 
     * @param unitId 医院ID
     * @param isOutUnit 是否为转出医院
     * @return 数量
     */
    long countByUnitId(String unitId, boolean isOutUnit);

    /**
     * 统计今日转诊表单数量
     * 
     * @return 今日转诊表单数量
     */
    long countTodayReferrals();

    /**
     * 统计待处理转诊表单数量
     * 
     * @return 待处理转诊表单数量
     */
    long countPendingReferrals();

    /**
     * 统计紧急转诊表单数量
     * 
     * @return 紧急转诊表单数量
     */
    long countUrgentReferrals();

    /**
     * 统计超时转诊表单数量
     * 
     * @param timeoutDays 超时天数
     * @return 超时转诊表单数量
     */
    long countTimeoutReferrals(int timeoutDays);

    /**
     * 批量确认转诊
     * 
     * @param ids 转诊表单ID列表
     * @param operatorId 操作人ID
     * @return 成功确认的数量
     */
    int batchConfirmReferrals(List<String> ids, String operatorId);

    /**
     * 批量取消转诊
     * 
     * @param ids 转诊表单ID列表
     * @param operatorId 操作人ID
     * @return 成功取消的数量
     */
    int batchCancelReferrals(List<String> ids, String operatorId);

    /**
     * 导出转诊表单数据
     * 
     * @param startDate 开始时间（可选）
     * @param endDate 结束时间（可选）
     * @param status 状态（可选）
     * @param unitId 医院ID（可选）
     * @return 转诊表单列表
     */
    List<ReferralFormEntityNew> exportReferralForms(LocalDateTime startDate, LocalDateTime endDate, 
                                                    Integer status, String unitId);

    /**
     * 导入转诊表单数据
     * 
     * @param referralForms 转诊表单列表
     * @param operatorId 操作人ID
     * @return 成功导入的数量
     */
    int importReferralForms(List<ReferralFormEntityNew> referralForms, String operatorId);

    /**
     * 清理过期数据
     * 
     * @param days 保留天数
     * @return 清理的记录数
     */
    int cleanupExpiredData(int days);

    /**
     * 自动处理超时转诊
     *
     * @param timeoutDays 超时天数
     * @return 处理的记录数
     */
    int autoProcessTimeoutReferrals(int timeoutDays);

    /**
     * 获取转诊统计信息
     *
     * @param unitId 医院ID（可选）
     * @param startDate 开始时间（可选）
     * @param endDate 结束时间（可选）
     * @return 统计信息Map
     */
    java.util.Map<String, Object> getReferralStatistics(String unitId, LocalDateTime startDate, LocalDateTime endDate);
}
