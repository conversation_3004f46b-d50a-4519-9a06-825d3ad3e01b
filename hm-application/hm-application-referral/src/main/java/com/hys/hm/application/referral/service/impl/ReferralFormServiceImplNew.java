package com.hys.hm.application.referral.service.impl;

import com.hys.hm.application.referral.service.ReferralFormServiceNew;
import com.hys.hm.infrastructure.persistence.referral.entity.ReferralFormEntityNew;
import com.hys.hm.infrastructure.persistence.referral.repository.ReferralFormRepositoryNew;
import com.hys.hm.shared.common.util.EncryptUtil;
import com.hys.hm.shared.framework.service.BaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 转诊表单服务实现类（使用新框架）
 * 继承BaseServiceImpl获得完整的CRUD和业务逻辑功能
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-29
 */
@Slf4j
@Service
@Transactional(readOnly = true)
public class ReferralFormServiceImplNew extends BaseServiceImpl<ReferralFormEntityNew, String> 
        implements ReferralFormServiceNew {

    @Autowired
    private ReferralFormRepositoryNew referralFormRepository;

    @Override
    public Optional<ReferralFormEntityNew> findByReferralNo(String referralNo) {
        Assert.hasText(referralNo, "转诊编号不能为空");
        return referralFormRepository.findByReferralNo(referralNo);
    }

    @Override
    public List<ReferralFormEntityNew> findByBasicInfoId(String basicInfoId) {
        Assert.hasText(basicInfoId, "基础信息ID不能为空");
        return referralFormRepository.findByBasicInfoId(basicInfoId);
    }

    @Override
    public List<ReferralFormEntityNew> findByOutUnitId(String outUnitId) {
        Assert.hasText(outUnitId, "转出医院ID不能为空");
        return referralFormRepository.findByOutUnitId(outUnitId);
    }

    @Override
    public List<ReferralFormEntityNew> findByInUnitId(String inUnitId) {
        Assert.hasText(inUnitId, "转入医院ID不能为空");
        return referralFormRepository.findByInUnitId(inUnitId);
    }

    @Override
    public List<ReferralFormEntityNew> findByDoctorId(String doctorId) {
        Assert.hasText(doctorId, "医生ID不能为空");
        return referralFormRepository.findByDoctorId(doctorId);
    }

    @Override
    public List<ReferralFormEntityNew> findByStatus(Integer status) {
        Assert.notNull(status, "状态不能为空");
        return referralFormRepository.findByStatus(status);
    }

    @Override
    public List<ReferralFormEntityNew> findByPatientNameLike(String patientName) {
        Assert.hasText(patientName, "患者姓名不能为空");
        return referralFormRepository.findByPatientNameContaining(patientName);
    }

    @Override
    public List<ReferralFormEntityNew> findByDateRange(LocalDateTime startDate, LocalDateTime endDate) {
        Assert.notNull(startDate, "开始时间不能为空");
        Assert.notNull(endDate, "结束时间不能为空");
        Assert.isTrue(!startDate.isAfter(endDate), "开始时间不能晚于结束时间");
        
        return referralFormRepository.findByReferralDateBetween(startDate, endDate);
    }

    @Override
    public List<ReferralFormEntityNew> findByIdCard(String idCard) {
        Assert.hasText(idCard, "身份证号不能为空");
        
        try {
            // 生成身份证号的搜索令牌
            String searchToken = EncryptUtil.generateSearchToken(idCard, 4);
            return referralFormRepository.findByIdCardToken(searchToken);
        } catch (Exception e) {
            log.warn("根据身份证号查询转诊表单失败: {}", e.getMessage());
            return new ArrayList<>();
        }
    }

    @Override
    public List<ReferralFormEntityNew> findByPhone(String phone) {
        Assert.hasText(phone, "手机号不能为空");
        
        try {
            // 生成手机号的搜索令牌
            String searchToken = EncryptUtil.generateSearchToken(phone, 3);
            return referralFormRepository.findByPhoneToken(searchToken);
        } catch (Exception e) {
            log.warn("根据手机号查询转诊表单失败: {}", e.getMessage());
            return new ArrayList<>();
        }
    }

    @Override
    public List<ReferralFormEntityNew> findPendingReferrals() {
        return referralFormRepository.findPendingReferrals();
    }

    @Override
    public List<ReferralFormEntityNew> findUrgentReferrals() {
        return referralFormRepository.findUrgentReferrals();
    }

    @Override
    public List<ReferralFormEntityNew> findTimeoutReferrals(int timeoutDays) {
        Assert.isTrue(timeoutDays > 0, "超时天数必须大于0");
        
        LocalDateTime cutoffTime = LocalDateTime.now().minusDays(timeoutDays);
        return referralFormRepository.findTimeoutReferrals(cutoffTime);
    }

    @Override
    public List<ReferralFormEntityNew> findTodayReferrals() {
        LocalDateTime startOfDay = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
        LocalDateTime endOfDay = startOfDay.plusDays(1).minusNanos(1);
        
        return referralFormRepository.findTodayReferrals(startOfDay, endOfDay);
    }

    @Override
    @Transactional
    public boolean confirmReferral(String id, String operatorId) {
        Assert.hasText(id, "转诊表单ID不能为空");
        Assert.hasText(operatorId, "操作人ID不能为空");
        
        Optional<ReferralFormEntityNew> referralOpt = findById(id);
        if (referralOpt.isPresent()) {
            ReferralFormEntityNew referral = referralOpt.get();
            
            if (!referral.isPending()) {
                throw new IllegalStateException("只有待处理状态的转诊表单才能确认");
            }
            
            referral.setStatus(2); // 已确认
            referral.setConfirmTime(LocalDateTime.now());
            referral.setRejectReason(null); // 清除拒绝原因
            referral.setUpdateBy(operatorId);
            
            save(referral);
            log.info("确认转诊成功: ID={}, 操作人={}", id, operatorId);
            return true;
        }
        
        log.warn("确认转诊失败，转诊表单不存在: ID={}", id);
        return false;
    }

    @Override
    @Transactional
    public boolean rejectReferral(String id, String rejectReason, String operatorId) {
        Assert.hasText(id, "转诊表单ID不能为空");
        Assert.hasText(rejectReason, "拒绝原因不能为空");
        Assert.hasText(operatorId, "操作人ID不能为空");
        
        Optional<ReferralFormEntityNew> referralOpt = findById(id);
        if (referralOpt.isPresent()) {
            ReferralFormEntityNew referral = referralOpt.get();
            
            if (!referral.isPending()) {
                throw new IllegalStateException("只有待处理状态的转诊表单才能拒绝");
            }
            
            referral.setStatus(3); // 已拒绝
            referral.setRejectReason(rejectReason);
            referral.setConfirmTime(LocalDateTime.now());
            referral.setUpdateBy(operatorId);
            
            save(referral);
            log.info("拒绝转诊成功: ID={}, 拒绝原因={}, 操作人={}", id, rejectReason, operatorId);
            return true;
        }
        
        log.warn("拒绝转诊失败，转诊表单不存在: ID={}", id);
        return false;
    }

    @Override
    @Transactional
    public boolean cancelReferral(String id, String operatorId) {
        Assert.hasText(id, "转诊表单ID不能为空");
        Assert.hasText(operatorId, "操作人ID不能为空");
        
        Optional<ReferralFormEntityNew> referralOpt = findById(id);
        if (referralOpt.isPresent()) {
            ReferralFormEntityNew referral = referralOpt.get();
            
            if (referral.isCompleted()) {
                throw new IllegalStateException("已完成的转诊表单不能取消");
            }
            
            referral.setStatus(4); // 已取消
            referral.setUpdateBy(operatorId);
            
            save(referral);
            log.info("取消转诊成功: ID={}, 操作人={}", id, operatorId);
            return true;
        }
        
        log.warn("取消转诊失败，转诊表单不存在: ID={}", id);
        return false;
    }

    @Override
    @Transactional
    public boolean completeReferral(String id, String operatorId) {
        Assert.hasText(id, "转诊表单ID不能为空");
        Assert.hasText(operatorId, "操作人ID不能为空");
        
        Optional<ReferralFormEntityNew> referralOpt = findById(id);
        if (referralOpt.isPresent()) {
            ReferralFormEntityNew referral = referralOpt.get();
            
            if (!referral.isConfirmed()) {
                throw new IllegalStateException("只有已确认状态的转诊表单才能完成");
            }
            
            referral.setStatus(5); // 已完成
            referral.setUpdateBy(operatorId);
            
            save(referral);
            log.info("完成转诊成功: ID={}, 操作人={}", id, operatorId);
            return true;
        }
        
        log.warn("完成转诊失败，转诊表单不存在: ID={}", id);
        return false;
    }

    @Override
    public String generateReferralNo() {
        // 生成格式：ZZ + YYYYMMDD + 4位序号
        String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String prefix = "ZZ" + dateStr + "%";
        
        String maxNo = referralFormRepository.findMaxReferralNoByPrefix(prefix);
        
        int sequence = 1;
        if (StringUtils.hasText(maxNo) && maxNo.length() >= 14) {
            try {
                String sequenceStr = maxNo.substring(10); // 取后4位
                sequence = Integer.parseInt(sequenceStr) + 1;
            } catch (NumberFormatException e) {
                log.warn("解析转诊编号序号失败: {}", maxNo);
            }
        }
        
        return String.format("ZZ%s%04d", dateStr, sequence);
    }

    @Override
    public boolean isReferralNoExists(String referralNo) {
        Assert.hasText(referralNo, "转诊编号不能为空");
        return referralFormRepository.existsByReferralNo(referralNo);
    }

    @Override
    public long countByStatus(Integer status) {
        Assert.notNull(status, "状态不能为空");
        return referralFormRepository.countByStatus(status);
    }

    @Override
    public long countByUnitId(String unitId, boolean isOutUnit) {
        Assert.hasText(unitId, "医院ID不能为空");
        
        if (isOutUnit) {
            return referralFormRepository.countByOutUnitId(unitId);
        } else {
            return referralFormRepository.countByInUnitId(unitId);
        }
    }

    @Override
    public long countTodayReferrals() {
        LocalDateTime startOfDay = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
        LocalDateTime endOfDay = startOfDay.plusDays(1).minusNanos(1);
        
        return referralFormRepository.countTodayReferrals(startOfDay, endOfDay);
    }

    @Override
    public long countPendingReferrals() {
        return countByStatus(1);
    }

    @Override
    public long countUrgentReferrals() {
        return referralFormRepository.findUrgentReferrals().size();
    }

    @Override
    public long countTimeoutReferrals(int timeoutDays) {
        return findTimeoutReferrals(timeoutDays).size();
    }

    @Override
    @Transactional
    public int batchConfirmReferrals(List<String> ids, String operatorId) {
        Assert.notEmpty(ids, "转诊表单ID列表不能为空");
        Assert.hasText(operatorId, "操作人ID不能为空");
        
        int successCount = 0;
        for (String id : ids) {
            try {
                if (confirmReferral(id, operatorId)) {
                    successCount++;
                }
            } catch (Exception e) {
                log.warn("批量确认转诊失败: ID={}, 错误={}", id, e.getMessage());
            }
        }
        
        log.info("批量确认转诊完成: 总数={}, 成功={}, 操作人={}", ids.size(), successCount, operatorId);
        return successCount;
    }

    @Override
    @Transactional
    public int batchCancelReferrals(List<String> ids, String operatorId) {
        Assert.notEmpty(ids, "转诊表单ID列表不能为空");
        Assert.hasText(operatorId, "操作人ID不能为空");
        
        int successCount = 0;
        for (String id : ids) {
            try {
                if (cancelReferral(id, operatorId)) {
                    successCount++;
                }
            } catch (Exception e) {
                log.warn("批量取消转诊失败: ID={}, 错误={}", id, e.getMessage());
            }
        }
        
        log.info("批量取消转诊完成: 总数={}, 成功={}, 操作人={}", ids.size(), successCount, operatorId);
        return successCount;
    }

    @Override
    public List<ReferralFormEntityNew> exportReferralForms(LocalDateTime startDate, LocalDateTime endDate,
                                                           Integer status, String unitId) {
        List<ReferralFormEntityNew> results = new ArrayList<>();

        if (startDate != null && endDate != null) {
            if (status != null) {
                results = referralFormRepository.findByDateRangeAndStatus(startDate, endDate, status);
            } else {
                results = findByDateRange(startDate, endDate);
            }
        } else if (status != null) {
            results = findByStatus(status);
        } else if (StringUtils.hasText(unitId)) {
            List<ReferralFormEntityNew> outResults = findByOutUnitId(unitId);
            List<ReferralFormEntityNew> inResults = findByInUnitId(unitId);
            results.addAll(outResults);
            results.addAll(inResults);
            // 去重
            results = results.stream().distinct().collect(Collectors.toList());
        } else {
            results = findAllNotDeleted();
        }

        log.info("导出转诊表单数据: {} 条", results.size());
        return results;
    }

    @Override
    @Transactional
    public int importReferralForms(List<ReferralFormEntityNew> referralForms, String operatorId) {
        Assert.notEmpty(referralForms, "转诊表单列表不能为空");
        Assert.hasText(operatorId, "操作人ID不能为空");

        int successCount = 0;
        for (ReferralFormEntityNew referral : referralForms) {
            try {
                // 生成新的ID和转诊编号
                referral.setId(UUID.randomUUID().toString().replace("-", ""));

                String referralNo = generateReferralNo();
                while (isReferralNoExists(referralNo)) {
                    referralNo = generateReferralNo();
                }
                referral.setReferralNo(referralNo);

                // 设置创建信息
                referral.setCreateBy(operatorId);
                referral.setUpdateBy(operatorId);

                save(referral);
                successCount++;
            } catch (Exception e) {
                log.warn("导入转诊表单失败: 患者={}, 错误={}", referral.getPatientName(), e.getMessage());
            }
        }

        log.info("导入转诊表单完成: 总数={}, 成功={}, 操作人={}", referralForms.size(), successCount, operatorId);
        return successCount;
    }

    @Override
    @Transactional
    public int cleanupExpiredData(int days) {
        Assert.isTrue(days > 0, "保留天数必须大于0");

        LocalDateTime cutoffTime = LocalDateTime.now().minusDays(days);
        List<ReferralFormEntityNew> expiredReferrals = referralFormRepository
                .findByReferralDateBetween(LocalDateTime.MIN, cutoffTime);

        int cleanedCount = 0;
        for (ReferralFormEntityNew referral : expiredReferrals) {
            if (softDeleteById(referral.getId())) {
                cleanedCount++;
            }
        }

        log.info("清理过期转诊数据完成: 保留天数={}, 清理数量={}", days, cleanedCount);
        return cleanedCount;
    }

    @Override
    @Transactional
    public int autoProcessTimeoutReferrals(int timeoutDays) {
        Assert.isTrue(timeoutDays > 0, "超时天数必须大于0");

        List<ReferralFormEntityNew> timeoutReferrals = findTimeoutReferrals(timeoutDays);
        int processedCount = 0;

        for (ReferralFormEntityNew referral : timeoutReferrals) {
            try {
                // 自动取消超时的转诊
                referral.setStatus(4); // 已取消
                referral.setRejectReason("系统自动取消：超时未处理");
                referral.setUpdateBy("SYSTEM");

                save(referral);
                processedCount++;
            } catch (Exception e) {
                log.warn("自动处理超时转诊失败: ID={}, 错误={}", referral.getId(), e.getMessage());
            }
        }

        log.info("自动处理超时转诊完成: 超时天数={}, 处理数量={}", timeoutDays, processedCount);
        return processedCount;
    }

    @Override
    public Map<String, Object> getReferralStatistics(String unitId, LocalDateTime startDate, LocalDateTime endDate) {
        Map<String, Object> statistics = new HashMap<>();

        // 基础统计
        statistics.put("totalCount", count());
        statistics.put("pendingCount", countPendingReferrals());
        statistics.put("confirmedCount", countByStatus(2));
        statistics.put("rejectedCount", countByStatus(3));
        statistics.put("cancelledCount", countByStatus(4));
        statistics.put("completedCount", countByStatus(5));
        statistics.put("todayCount", countTodayReferrals());
        statistics.put("urgentCount", countUrgentReferrals());
        statistics.put("timeoutCount", countTimeoutReferrals(7));

        // 医院相关统计
        if (StringUtils.hasText(unitId)) {
            statistics.put("outUnitCount", countByUnitId(unitId, true));
            statistics.put("inUnitCount", countByUnitId(unitId, false));
        }

        // 时间范围统计
        if (startDate != null && endDate != null) {
            List<ReferralFormEntityNew> rangeReferrals = findByDateRange(startDate, endDate);
            statistics.put("rangeCount", rangeReferrals.size());

            Map<Integer, Long> statusCount = rangeReferrals.stream()
                    .collect(Collectors.groupingBy(ReferralFormEntityNew::getStatus, Collectors.counting()));
            statistics.put("rangeStatusCount", statusCount);
        }

        log.debug("获取转诊统计信息: {}", statistics);
        return statistics;
    }

    // 业务逻辑钩子方法的重写

    @Override
    public void validate(ReferralFormEntityNew entity) {
        Assert.notNull(entity, "转诊表单不能为空");
        Assert.hasText(entity.getBasicInfoId(), "基础信息ID不能为空");
        Assert.hasText(entity.getPatientName(), "患者姓名不能为空");
        Assert.hasText(entity.getPhone(), "联系电话不能为空");
        Assert.notNull(entity.getGender(), "性别不能为空");
        Assert.notNull(entity.getAge(), "年龄不能为空");
        Assert.hasText(entity.getReferralReason(), "转诊原因不能为空");
        Assert.hasText(entity.getOutUnitId(), "转出医院ID不能为空");
        Assert.hasText(entity.getOutUnitName(), "转出医院名称不能为空");
        Assert.hasText(entity.getInUnitId(), "转入医院ID不能为空");
        Assert.hasText(entity.getInUnitName(), "转入医院名称不能为空");

        // 验证性别值
        if (entity.getGender() != 1 && entity.getGender() != 2) {
            throw new IllegalArgumentException("性别值只能为1（男）或2（女）");
        }

        // 验证年龄范围
        if (entity.getAge() < 0 || entity.getAge() > 150) {
            throw new IllegalArgumentException("年龄必须在0-150之间");
        }

        // 验证状态值
        if (entity.getStatus() != null && (entity.getStatus() < 1 || entity.getStatus() > 5)) {
            throw new IllegalArgumentException("状态值必须在1-5之间");
        }

        // 验证紧急程度
        if (entity.getUrgencyLevel() != null && (entity.getUrgencyLevel() < 1 || entity.getUrgencyLevel() > 3)) {
            throw new IllegalArgumentException("紧急程度必须在1-3之间");
        }
    }

    @Override
    public void beforeSave(ReferralFormEntityNew entity) {
        // 如果没有设置ID，自动生成
        if (!StringUtils.hasText(entity.getId())) {
            entity.setId(UUID.randomUUID().toString().replace("-", ""));
        }

        // 如果没有设置转诊编号，自动生成
        if (!StringUtils.hasText(entity.getReferralNo())) {
            String referralNo = generateReferralNo();
            while (isReferralNoExists(referralNo)) {
                referralNo = generateReferralNo();
            }
            entity.setReferralNo(referralNo);
        }

        // 如果没有设置转诊日期，使用当前时间
        if (entity.getReferralDate() == null) {
            entity.setReferralDate(LocalDateTime.now());
        }

        // 如果没有设置状态，默认为待处理
        if (entity.getStatus() == null) {
            entity.setStatus(1);
        }

        // 如果没有设置紧急程度，默认为普通
        if (entity.getUrgencyLevel() == null) {
            entity.setUrgencyLevel(1);
        }

        log.debug("保存前处理完成: ID={}, 转诊编号={}, 患者={}",
                entity.getId(), entity.getReferralNo(), entity.getPatientName());
    }

    @Override
    public void afterSave(ReferralFormEntityNew entity) {
        log.info("转诊表单保存成功: ID={}, 转诊编号={}, 患者={}",
                entity.getId(), entity.getReferralNo(), entity.getPatientName());

        // 可以在这里添加保存后的业务逻辑，如发送通知、更新缓存等
        refreshCache();
    }

    @Override
    public void beforeUpdate(ReferralFormEntityNew entity) {
        log.debug("更新前处理: ID={}, 转诊编号={}, 患者={}",
                entity.getId(), entity.getReferralNo(), entity.getPatientName());

        // 可以在这里添加更新前的业务逻辑
    }

    @Override
    public void afterUpdate(ReferralFormEntityNew entity) {
        log.info("转诊表单更新成功: ID={}, 转诊编号={}, 患者={}",
                entity.getId(), entity.getReferralNo(), entity.getPatientName());

        // 可以在这里添加更新后的业务逻辑
        refreshCache();
    }

    @Override
    public void beforeDelete(String id) {
        log.info("准备删除转诊表单: ID={}", id);

        // 可以在这里添加删除前的业务逻辑，如检查关联数据
    }

    @Override
    public void afterDelete(String id) {
        log.info("转诊表单删除成功: ID={}", id);

        // 可以在这里添加删除后的业务逻辑
        refreshCache();
    }

    @Override
    public void refreshCache() {
        // 刷新缓存的具体实现
        log.debug("刷新转诊表单缓存");
    }

    @Override
    public void clearCache() {
        // 清空缓存的具体实现
        log.debug("清空转诊表单缓存");
    }
}
