package com.hys.hm.application.referral.converter;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import java.lang.reflect.Method;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ReferralDTOConverter测试类
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-29
 */
class ReferralDTOConverterTest {
    
    private ReferralDTOConverter converter;
    private Method maskPhoneMethod;
    private Method maskIdCardMethod;
    
    @BeforeEach
    void setUp() throws Exception {
        converter = new ReferralDTOConverter();
        
        // 获取私有方法用于测试
        maskPhoneMethod = ReferralDTOConverter.class.getDeclaredMethod("maskPhone", String.class);
        maskPhoneMethod.setAccessible(true);
        
        maskIdCardMethod = ReferralDTOConverter.class.getDeclaredMethod("maskIdCard", String.class);
        maskIdCardMethod.setAccessible(true);
    }
    
    @Test
    @DisplayName("测试手机号脱敏 - null值")
    void testMaskPhone_Null() throws Exception {
        String result = (String) maskPhoneMethod.invoke(converter, (String) null);
        assertNull(result);
    }
    
    @Test
    @DisplayName("测试手机号脱敏 - 长度小于7")
    void testMaskPhone_LessThan7() throws Exception {
        String phone = "123456";
        String result = (String) maskPhoneMethod.invoke(converter, phone);
        assertEquals("123456", result);
    }
    
    @Test
    @DisplayName("测试手机号脱敏 - 长度等于7")
    void testMaskPhone_Equals7() throws Exception {
        String phone = "1234567";
        String result = (String) maskPhoneMethod.invoke(converter, phone);
        assertEquals("123****", result);
    }
    
    @Test
    @DisplayName("测试手机号脱敏 - 标准11位手机号")
    void testMaskPhone_Standard11Digits() throws Exception {
        String phone = "13812345678";
        String result = (String) maskPhoneMethod.invoke(converter, phone);
        assertEquals("138****5678", result);
    }
    
    @Test
    @DisplayName("测试手机号脱敏 - 8位手机号")
    void testMaskPhone_8Digits() throws Exception {
        String phone = "12345678";
        String result = (String) maskPhoneMethod.invoke(converter, phone);
        assertEquals("123****5678", result);
    }
    
    @Test
    @DisplayName("测试手机号脱敏 - 10位手机号")
    void testMaskPhone_10Digits() throws Exception {
        String phone = "1234567890";
        String result = (String) maskPhoneMethod.invoke(converter, phone);
        assertEquals("123****7890", result);
    }
    
    @Test
    @DisplayName("测试手机号脱敏 - 12位手机号")
    void testMaskPhone_12Digits() throws Exception {
        String phone = "123456789012";
        String result = (String) maskPhoneMethod.invoke(converter, phone);
        assertEquals("123****9012", result);
    }
    
    @Test
    @DisplayName("测试身份证脱敏 - null值")
    void testMaskIdCard_Null() throws Exception {
        String result = (String) maskIdCardMethod.invoke(converter, (String) null);
        assertNull(result);
    }
    
    @Test
    @DisplayName("测试身份证脱敏 - 长度小于8")
    void testMaskIdCard_LessThan8() throws Exception {
        String idCard = "1234567";
        String result = (String) maskIdCardMethod.invoke(converter, idCard);
        assertEquals("1234567", result);
    }
    
    @Test
    @DisplayName("测试身份证脱敏 - 标准18位身份证")
    void testMaskIdCard_Standard18Digits() throws Exception {
        String idCard = "123456789012345678";
        String result = (String) maskIdCardMethod.invoke(converter, idCard);
        assertEquals("1234**********5678", result);
    }
    
    @Test
    @DisplayName("测试身份证脱敏 - 15位身份证")
    void testMaskIdCard_15Digits() throws Exception {
        String idCard = "123456789012345";
        String result = (String) maskIdCardMethod.invoke(converter, idCard);
        assertEquals("1234**********2345", result);
    }
}
