package com.hys.hm.application.referral.service;

import com.hys.hm.application.referral.dto.ReferralCreateDTO;
import com.hys.hm.application.referral.dto.ReferralQueryDTO;
import com.hys.hm.shared.types.dto.ReferralSummaryDTO;
import com.hys.hm.shared.types.enums.ReferralStatus;
import com.hys.hm.shared.types.enums.UrgencyLevel;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 转诊应用服务测试类
 * 展示如何使用新的转诊模块功能
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-29
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
class ReferralApplicationServiceTest {
    
    @Autowired
    private ReferralApplicationService referralApplicationService;
    
    @Test
    @DisplayName("创建转诊表单 - 完整流程测试")
    void testCreateReferralForm() {
        // Given - 准备创建转诊表单的数据
        ReferralCreateDTO createDTO = buildTestCreateDTO();
        String operatorId = "test-operator-001";
        
        // When - 创建转诊表单
        ReferralQueryDTO result = referralApplicationService.createReferralForm(createDTO, operatorId);
        
        // Then - 验证创建结果
        assertNotNull(result);
        assertNotNull(result.getId());
        assertNotNull(result.getReferralNo());
        assertEquals(createDTO.getPatientName(), result.getPatientName());
        assertEquals(createDTO.getReferralReason(), result.getReferralReason());
        assertEquals(ReferralStatus.PENDING, result.getStatus());
        assertEquals(operatorId, result.getCreateBy());
        
        System.out.println("✅ 转诊表单创建成功:");
        System.out.println("   ID: " + result.getId());
        System.out.println("   编号: " + result.getReferralNo());
        System.out.println("   患者: " + result.getPatientName());
        System.out.println("   状态: " + result.getStatusDesc());
    }
    
    @Test
    @DisplayName("转诊状态流转 - 完整业务流程测试")
    void testReferralStatusFlow() {
        // Given - 创建转诊表单
        ReferralCreateDTO createDTO = buildTestCreateDTO();
        String operatorId = "test-operator-001";
        ReferralQueryDTO referral = referralApplicationService.createReferralForm(createDTO, operatorId);
        
        // When & Then - 测试确认转诊
        ReferralQueryDTO confirmedReferral = referralApplicationService.confirmReferral(referral.getId(), operatorId);
        assertEquals(ReferralStatus.CONFIRMED, confirmedReferral.getStatus());
        assertNotNull(confirmedReferral.getConfirmTime());
        
        System.out.println("✅ 转诊确认成功:");
        System.out.println("   状态: " + confirmedReferral.getStatusDesc());
        System.out.println("   确认时间: " + confirmedReferral.getConfirmTime());
        
        // When & Then - 测试完成转诊
        ReferralQueryDTO completedReferral = referralApplicationService.completeReferral(referral.getId(), operatorId);
        assertEquals(ReferralStatus.COMPLETED, completedReferral.getStatus());
        
        System.out.println("✅ 转诊完成成功:");
        System.out.println("   最终状态: " + completedReferral.getStatusDesc());
    }
    
    @Test
    @DisplayName("转诊拒绝流程测试")
    void testRejectReferral() {
        // Given - 创建转诊表单
        ReferralCreateDTO createDTO = buildTestCreateDTO();
        String operatorId = "test-operator-001";
        ReferralQueryDTO referral = referralApplicationService.createReferralForm(createDTO, operatorId);
        
        // When - 拒绝转诊
        String rejectReason = "医疗资源不足，暂时无法接收";
        ReferralQueryDTO rejectedReferral = referralApplicationService.rejectReferral(
                referral.getId(), rejectReason, operatorId);
        
        // Then - 验证拒绝结果
        assertEquals(ReferralStatus.REJECTED, rejectedReferral.getStatus());
        assertEquals(rejectReason, rejectedReferral.getRejectReason());
        assertNotNull(rejectedReferral.getConfirmTime());
        
        System.out.println("✅ 转诊拒绝成功:");
        System.out.println("   状态: " + rejectedReferral.getStatusDesc());
        System.out.println("   拒绝原因: " + rejectedReferral.getRejectReason());
    }
    
    @Test
    @DisplayName("查询功能测试")
    void testQueryFunctions() {
        // Given - 创建多个转诊表单
        String operatorId = "test-operator-001";
        String patientId = "patient-test-001";
        
        // 创建普通转诊
        ReferralCreateDTO normalDTO = buildTestCreateDTO();
        normalDTO.setBasicInfoId(patientId);
        normalDTO.setUrgencyLevel(UrgencyLevel.NORMAL);
        ReferralQueryDTO normalReferral = referralApplicationService.createReferralForm(normalDTO, operatorId);
        
        // 创建紧急转诊
        ReferralCreateDTO urgentDTO = buildTestCreateDTO();
        urgentDTO.setBasicInfoId(patientId);
        urgentDTO.setPatientName("李四");
        urgentDTO.setUrgencyLevel(UrgencyLevel.URGENT);
        ReferralQueryDTO urgentReferral = referralApplicationService.createReferralForm(urgentDTO, operatorId);
        
        // When & Then - 测试各种查询功能
        
        // 1. 根据ID查询
        Optional<ReferralQueryDTO> foundReferral = referralApplicationService.getReferralDetail(normalReferral.getId());
        assertTrue(foundReferral.isPresent());
        assertEquals(normalReferral.getId(), foundReferral.get().getId());
        
        // 2. 根据转诊编号查询
        Optional<ReferralQueryDTO> foundByNo = referralApplicationService.getReferralDetailByNo(normalReferral.getReferralNo());
        assertTrue(foundByNo.isPresent());
        assertEquals(normalReferral.getReferralNo(), foundByNo.get().getReferralNo());
        
        // 3. 获取患者转诊记录
        List<ReferralSummaryDTO> patientReferrals = referralApplicationService.getPatientReferralSummary(patientId);
        assertEquals(2, patientReferrals.size());
        
        // 4. 获取待处理转诊
        List<ReferralSummaryDTO> pendingReferrals = referralApplicationService.getPendingReferrals();
        assertTrue(pendingReferrals.size() >= 2);
        
        // 5. 获取紧急转诊
        List<ReferralSummaryDTO> urgentReferrals = referralApplicationService.getUrgentReferrals();
        assertTrue(urgentReferrals.stream().anyMatch(r -> r.getReferralId().equals(urgentReferral.getId())));
        
        // 6. 获取今日转诊
        List<ReferralSummaryDTO> todayReferrals = referralApplicationService.getTodayReferrals();
        assertTrue(todayReferrals.size() >= 2);
        
        System.out.println("✅ 查询功能测试成功:");
        System.out.println("   患者转诊记录数: " + patientReferrals.size());
        System.out.println("   待处理转诊数: " + pendingReferrals.size());
        System.out.println("   紧急转诊数: " + urgentReferrals.size());
        System.out.println("   今日转诊数: " + todayReferrals.size());
    }
    
    @Test
    @DisplayName("批量操作测试")
    void testBatchOperations() {
        // Given - 创建多个转诊表单
        String operatorId = "test-operator-001";
        
        ReferralQueryDTO referral1 = referralApplicationService.createReferralForm(buildTestCreateDTO(), operatorId);
        ReferralQueryDTO referral2 = referralApplicationService.createReferralForm(buildTestCreateDTO(), operatorId);
        ReferralQueryDTO referral3 = referralApplicationService.createReferralForm(buildTestCreateDTO(), operatorId);
        
        List<String> referralIds = List.of(referral1.getId(), referral2.getId(), referral3.getId());
        
        // When - 批量确认转诊
        int successCount = referralApplicationService.batchConfirmReferrals(referralIds, operatorId);
        
        // Then - 验证批量操作结果
        assertEquals(3, successCount);
        
        // 验证每个转诊都已确认
        for (String id : referralIds) {
            Optional<ReferralQueryDTO> referral = referralApplicationService.getReferralDetail(id);
            assertTrue(referral.isPresent());
            assertEquals(ReferralStatus.CONFIRMED, referral.get().getStatus());
        }
        
        System.out.println("✅ 批量确认成功:");
        System.out.println("   成功确认数量: " + successCount);
    }
    
    /**
     * 构建测试用的创建DTO
     */
    private ReferralCreateDTO buildTestCreateDTO() {
        return ReferralCreateDTO.builder()
                .basicInfoId("patient-test-001")
                .patientName("张三")
                .gender(1)
                .age(45)
                .idCard("110101198001011234")
                .phone("13800138000")
                .address("北京市朝阳区")
                .referralReason("心脏病需要专科治疗")
                .outUnitId("hospital-001")
                .outUnitName("北京市第一医院")
                .outDeptName("心内科")
                .outDoctorName("王医生")
                .inUnitId("hospital-002")
                .inUnitName("北京协和医院")
                .inDeptName("心外科")
                .inDoctorName("李医生")
                .urgencyLevel(UrgencyLevel.NORMAL)
                .referralDate(LocalDateTime.now())
                .build();
    }
}
