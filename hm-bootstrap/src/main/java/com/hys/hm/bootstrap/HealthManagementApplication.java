package com.hys.hm.bootstrap;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 健康管理系统启动类
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-23
 */
@SpringBootApplication(scanBasePackages = {
    "com.hys.hm.bootstrap",
    "com.hys.hm.interfaces.web",
    "com.hys.hm.application",
    "com.hys.hm.domain",
    "com.hys.hm.infrastructure",
    "com.hys.hm.shared.common",
    "com.hys.hm.shared.framework",
    "com.hys.hm.shared.events"
})
@EnableJpaRepositories(basePackages = {
    "com.hys.hm.infrastructure.persistence",
    "com.hys.hm.infrastructure.logging.repository"
})
@EntityScan(basePackages = {
    "com.hys.hm.infrastructure.persistence",
    "com.hys.hm.infrastructure.logging.entity"
})
@EnableJpaAuditing
@EnableScheduling
@EnableConfigurationProperties
public class HealthManagementApplication {
    
    public static void main(String[] args) {
        SpringApplication.run(HealthManagementApplication.class, args);
    }
}
