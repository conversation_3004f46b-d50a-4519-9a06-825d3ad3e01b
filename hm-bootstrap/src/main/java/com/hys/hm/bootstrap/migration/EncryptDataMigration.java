package com.hys.hm.bootstrap.migration;

import com.hys.hm.shared.common.service.EncryptService;
import com.hys.hm.shared.common.annotation.EncryptField;
import com.hys.hm.infrastructure.persistence.service.EncryptFieldService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * 加密数据迁移
 * 处理现有数据的加密和索引创建
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-23
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "app.migration", name = "encrypt-data", havingValue = "true", matchIfMissing = false)
public class EncryptDataMigration implements CommandLineRunner {
    
    private final JdbcTemplate jdbcTemplate;
    private final EncryptService encryptService;
    private final EncryptFieldService encryptFieldService;
    
    @Override
    @Transactional
    public void run(String... args) throws Exception {
        log.info("开始执行加密数据迁移...");
        
        try {
            // 1. 处理转诊表单数据
            migrateReferralFormData();
            
            log.info("加密数据迁移完成！");
            
        } catch (Exception e) {
            log.error("加密数据迁移失败: {}", e.getMessage(), e);
            throw e;
        }
    }
    
    /**
     * 迁移转诊表单数据
     */
    private void migrateReferralFormData() {
        log.info("开始迁移转诊表单数据...");
        
        // 1. 先备份现有数据
        backupReferralFormData();
        
        // 2. 扩展字段长度
        expandFieldLengths();
        
        // 3. 加密现有数据
        encryptExistingData();
        
        // 4. 创建搜索索引
        createSearchIndexes();
        
        log.info("转诊表单数据迁移完成");
    }
    
    /**
     * 备份现有数据
     */
    private void backupReferralFormData() {
        log.info("备份转诊表单数据...");
        
        try {
            // 创建备份表
            String createBackupTableSql = """
                CREATE TABLE IF NOT EXISTS dc_referral_form_backup_20250723 AS 
                SELECT * FROM dc_referral_form
                """;
            
            jdbcTemplate.execute(createBackupTableSql);
            
            // 统计备份数据量
            Integer backupCount = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM dc_referral_form_backup_20250723", Integer.class);
            
            log.info("数据备份完成，备份记录数: {}", backupCount);
            
        } catch (Exception e) {
            log.error("数据备份失败: {}", e.getMessage(), e);
            throw new RuntimeException("数据备份失败", e);
        }
    }
    
    /**
     * 扩展字段长度
     */
    private void expandFieldLengths() {
        log.info("扩展字段长度...");
        
        try {
            // 扩展身份证号字段长度
            jdbcTemplate.execute(
                "ALTER TABLE dc_referral_form MODIFY COLUMN id_card VARCHAR(500) COMMENT '身份证号（加密存储）'"
            );
            
            // 扩展手机号字段长度
            jdbcTemplate.execute(
                "ALTER TABLE dc_referral_form MODIFY COLUMN phone VARCHAR(500) NOT NULL COMMENT '联系电话（加密存储）'"
            );
            
            log.info("字段长度扩展完成");
            
        } catch (Exception e) {
            log.warn("字段长度扩展可能已存在: {}", e.getMessage());
        }
    }
    
    /**
     * 加密现有数据
     */
    private void encryptExistingData() {
        log.info("加密现有数据...");
        
        try {
            // 查询所有需要加密的记录
            String selectSql = """
                SELECT id, id_card, phone 
                FROM dc_referral_form 
                WHERE id_card IS NOT NULL OR phone IS NOT NULL
                """;
            
            List<Map<String, Object>> records = jdbcTemplate.queryForList(selectSql);
            
            log.info("找到 {} 条需要加密的记录", records.size());
            
            int processedCount = 0;
            for (Map<String, Object> record : records) {
                String id = (String) record.get("id");
                String idCard = (String) record.get("id_card");
                String phone = (String) record.get("phone");
                
                try {
                    // 加密身份证号
                    String encryptedIdCard = null;
                    if (idCard != null && !idCard.trim().isEmpty()) {
                        // 检查是否已经加密（简单判断：加密后的数据通常比较长且包含特殊字符）
                        if (idCard.length() < 100) {
                            encryptedIdCard = encryptService.encrypt(idCard, EncryptField.EncryptType.AES);
                        } else {
                            encryptedIdCard = idCard; // 已经加密
                        }
                    }
                    
                    // 加密手机号
                    String encryptedPhone = null;
                    if (phone != null && !phone.trim().isEmpty()) {
                        // 检查是否已经加密
                        if (phone.length() < 100) {
                            encryptedPhone = encryptService.encrypt(phone, EncryptField.EncryptType.AES);
                        } else {
                            encryptedPhone = phone; // 已经加密
                        }
                    }
                    
                    // 更新数据库
                    String updateSql = """
                        UPDATE dc_referral_form 
                        SET id_card = ?, phone = ? 
                        WHERE id = ?
                        """;
                    
                    jdbcTemplate.update(updateSql, encryptedIdCard, encryptedPhone, id);
                    
                    processedCount++;
                    
                    if (processedCount % 100 == 0) {
                        log.info("已处理 {} 条记录", processedCount);
                    }
                    
                } catch (Exception e) {
                    log.error("加密记录失败，ID: {}, 错误: {}", id, e.getMessage());
                }
            }
            
            log.info("数据加密完成，共处理 {} 条记录", processedCount);
            
        } catch (Exception e) {
            log.error("数据加密失败: {}", e.getMessage(), e);
            throw new RuntimeException("数据加密失败", e);
        }
    }
    
    /**
     * 创建搜索索引
     */
    private void createSearchIndexes() {
        log.info("创建搜索索引...");
        
        try {
            // 查询所有转诊表单记录
            String selectSql = """
                SELECT id, id_card, phone 
                FROM dc_referral_form 
                WHERE id_card IS NOT NULL OR phone IS NOT NULL
                """;
            
            List<Map<String, Object>> records = jdbcTemplate.queryForList(selectSql);
            
            log.info("为 {} 条记录创建搜索索引", records.size());
            
            int indexedCount = 0;
            for (Map<String, Object> record : records) {
                String id = (String) record.get("id");
                String encryptedIdCard = (String) record.get("id_card");
                String encryptedPhone = (String) record.get("phone");
                
                try {
                    // 解密数据以创建索引
                    if (encryptedIdCard != null && !encryptedIdCard.trim().isEmpty()) {
                        String plainIdCard = encryptService.decrypt(encryptedIdCard, EncryptField.EncryptType.AES);
                        createFieldIndex("ReferralFormEntity", id, "idCard", plainIdCard, true, 4);
                    }
                    
                    if (encryptedPhone != null && !encryptedPhone.trim().isEmpty()) {
                        String plainPhone = encryptService.decrypt(encryptedPhone, EncryptField.EncryptType.AES);
                        createFieldIndex("ReferralFormEntity", id, "phone", plainPhone, true, 3);
                    }
                    
                    indexedCount++;
                    
                    if (indexedCount % 100 == 0) {
                        log.info("已创建 {} 条索引", indexedCount);
                    }
                    
                } catch (Exception e) {
                    log.error("创建索引失败，ID: {}, 错误: {}", id, e.getMessage());
                }
            }
            
            log.info("搜索索引创建完成，共创建 {} 条索引", indexedCount);
            
        } catch (Exception e) {
            log.error("创建搜索索引失败: {}", e.getMessage(), e);
            throw new RuntimeException("创建搜索索引失败", e);
        }
    }
    
    /**
     * 为单个字段创建索引
     */
    private void createFieldIndex(String entityType, String entityId, String fieldName, 
                                 String plainText, boolean fuzzySearch, int tokenLength) {
        
        // 删除旧索引
        String deleteSql = """
            DELETE FROM encrypt_search_index 
            WHERE entity_type = ? AND entity_id = ? AND field_name = ?
            """;
        jdbcTemplate.update(deleteSql, entityType, entityId, fieldName);
        
        // 创建精确查询索引
        String exactHash = encryptService.generateExactHash(plainText);
        String insertExactSql = """
            INSERT INTO encrypt_search_index 
            (entity_type, entity_id, field_name, exact_hash, create_time, update_time) 
            VALUES (?, ?, ?, ?, NOW(), NOW())
            """;
        jdbcTemplate.update(insertExactSql, entityType, entityId, fieldName, exactHash);
        
        // 如果支持模糊查询，创建分词索引
        if (fuzzySearch) {
            List<String> tokenHashes = encryptService.generateFuzzyTokens(plainText, tokenLength);
            
            String insertTokenSql = """
                INSERT INTO encrypt_search_index 
                (entity_type, entity_id, field_name, token_hash, create_time, update_time) 
                VALUES (?, ?, ?, ?, NOW(), NOW())
                """;
            
            for (String tokenHash : tokenHashes) {
                jdbcTemplate.update(insertTokenSql, entityType, entityId, fieldName, tokenHash);
            }
        }
    }
}
