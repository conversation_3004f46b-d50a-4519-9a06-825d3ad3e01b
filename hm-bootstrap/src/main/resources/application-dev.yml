# 开发环境配置
server:
  port: 8080
  servlet:
    context-path: /hm-dev

spring:
  # 数据库配置 - 开发环境
  datasource:
    url: jdbc:mysql://${DB_HOST:**************}:${DB_PORT:3306}/${DB_NAME:hm_dev}?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:IOE6rY6HsAsgtyqK}
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 5
      minimum-idle: 1
      connection-timeout: 30000
      idle-timeout: 300000
      max-lifetime: 900000
      pool-name: HM-Dev-HikariCP
      auto-commit: true
      connection-test-query: SELECT 1

  # JPA/Hibernate配置 - 开发环境
  jpa:
    hibernate:
      ddl-auto: update  # 开发环境允许自动更新表结构
    show-sql: true      # 开发环境显示SQL
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true
        use_sql_comments: true
        jdbc:
          batch_size: 5
          fetch_size: 10
        order_inserts: true
        order_updates: true
        batch_versioned_data: true
        cache:
          use_second_level_cache: false
          use_query_cache: false
        generate_statistics: true  # 开发环境开启统计

  # Redis配置 - 开发环境
  data:
    redis:
      host: ${REDIS_HOST:**************}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:keyan}
      database: 1
      timeout: 3000
      lettuce:
        pool:
          max-active: 2
          max-idle: 2
          min-idle: 0
          max-wait: -1

    # MongoDB配置 - 开发环境
    mongodb:
      uri: mongodb://${MONGO_USERNAME:test}:${MONGO_PASSWORD:Llwr6LlXfnKBH!gu}@${MONGO_HOST:**************}:${MONGO_PORT:27029}/${MONGO_DATABASE:test}
      auto-index-creation: true

  # 缓存配置
  cache:
    type: redis
    redis:
      time-to-live: 60000  # 1分钟缓存（开发环境短缓存）
      cache-null-values: false

  # Flyway数据库迁移配置
  flyway:
    enabled: false
    locations: classpath:db/migration
    baseline-on-migrate: true
    validate-on-migrate: false  # 开发环境不验证迁移
    encoding: UTF-8
    clean-disabled: false  # 开发环境允许清理

# 日志配置 - 开发环境
logging:
  level:
    com.hys.hm: DEBUG
    org.springframework.cache: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
    org.springframework.web: DEBUG
    org.springframework.security: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"
  file:
    name: logs/hm-dev.log
    max-size: 50MB
    max-history: 7

# 管理端点配置 - 开发环境
management:
  endpoints:
    web:
      exposure:
        include: "*"  # 开发环境暴露所有端点
  endpoint:
    health:
      show-details: always
    shutdown:
      enabled: true  # 开发环境允许远程关闭

# 应用信息
info:
  app:
    name: 健康管理系统-开发环境
    description: 基于Spring Boot的健康管理系统开发环境
    version: 0.0.1-SNAPSHOT
    environment: dev
    encoding: UTF-8
    java:
      version: 21

# 自定义配置
hm:
  # 日志配置
  logging:
    enabled: true
    async: true
    level: DEBUG
    retention-days: 7

  # 安全配置
  security:
    jwt:
      secret: ${JWT_SECRET:dev_jwt_secret_key_for_health_management_system}
      expiration: 86400  # 24小时（开发环境长时间）

  # 文件上传配置
  file:
    upload:
      path: ${FILE_UPLOAD_PATH:/tmp/hm-dev/uploads}
      max-size: 50MB  # 开发环境允许大文件
      allowed-types: "*"  # 开发环境允许所有文件类型

  # 外部服务配置
  external:
    ai:
      enabled: true
      api-key: ${AI_API_KEY:dev_ai_key}
      timeout: 60000  # 开发环境长超时

    sms:
      enabled: false  # 开发环境关闭短信
      provider: mock

    email:
      enabled: false  # 开发环境关闭邮件
      smtp:
        host: ${SMTP_HOST:localhost}
        port: ${SMTP_PORT:1025}  # 使用MailHog等开发工具
        username: ${SMTP_USERNAME:dev@localhost}
        password: ${SMTP_PASSWORD:}

  # 开发环境特殊配置
  dev:
    # 热重载配置
    hot-reload:
      enabled: true

    # 调试配置
    debug:
      sql-logging: true
      request-logging: true
      response-logging: true

    # 测试数据
    test-data:
      auto-create: true  # 自动创建测试数据
      reset-on-startup: false

  # 数据迁移配置
  migration:
    # 是否执行加密数据迁移（首次启动时设置为true）
    encrypt-data: true
