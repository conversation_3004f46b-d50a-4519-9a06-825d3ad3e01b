package com.hys.hm.domain.referral.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 医院信息值对象
 * 转诊表单中的医院相关信息
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-29
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HospitalInfo {
    
    /**
     * 医院ID
     */
    private String unitId;
    
    /**
     * 医院名称
     */
    private String unitName;
    
    /**
     * 科室ID
     */
    private String deptId;
    
    /**
     * 科室名称
     */
    private String deptName;
    
    /**
     * 医生ID
     */
    private String doctorId;
    
    /**
     * 医生姓名
     */
    private String doctorName;
    
    /**
     * 医生联系方式
     */
    private String doctorPhone;
    
    /**
     * 验证医院信息完整性
     */
    public void validate() {
        if (unitId == null || unitId.trim().isEmpty()) {
            throw new IllegalArgumentException("医院ID不能为空");
        }
        
        if (unitName == null || unitName.trim().isEmpty()) {
            throw new IllegalArgumentException("医院名称不能为空");
        }
    }
    
    /**
     * 判断是否有科室信息
     */
    public boolean hasDepartmentInfo() {
        return deptId != null && !deptId.trim().isEmpty() &&
               deptName != null && !deptName.trim().isEmpty();
    }
    
    /**
     * 判断是否有医生信息
     */
    public boolean hasDoctorInfo() {
        return doctorId != null && !doctorId.trim().isEmpty() &&
               doctorName != null && !doctorName.trim().isEmpty();
    }
    
    /**
     * 获取完整的医院科室描述
     */
    public String getFullDescription() {
        StringBuilder sb = new StringBuilder();
        sb.append(unitName);
        
        if (hasDepartmentInfo()) {
            sb.append(" - ").append(deptName);
        }
        
        if (hasDoctorInfo()) {
            sb.append(" - ").append(doctorName);
        }
        
        return sb.toString();
    }
}
