package com.hys.hm.domain.referral.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 医疗信息值对象
 * 转诊表单中的医疗相关信息
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-29
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MedicalInfo {
    
    /**
     * 主要症状
     */
    private String mainSymptoms;
    
    /**
     * 初步诊断
     */
    private String preliminaryDiagnosis;
    
    /**
     * 病史摘要
     */
    private String medicalHistory;
    
    /**
     * 检查结果
     */
    private String examResults;
    
    /**
     * 治疗经过
     */
    private String treatmentHistory;
    
    /**
     * 转诊原因
     */
    private String referralReason;
    
    /**
     * 转诊目的
     */
    private String referralPurpose;
    
    /**
     * 验证医疗信息完整性
     */
    public void validate() {
        if (referralReason == null || referralReason.trim().isEmpty()) {
            throw new IllegalArgumentException("转诊原因不能为空");
        }
        
        if (referralReason.length() > 1000) {
            throw new IllegalArgumentException("转诊原因长度不能超过1000个字符");
        }
    }
    
    /**
     * 判断是否有完整的病史信息
     */
    public boolean hasCompleteHistory() {
        return mainSymptoms != null && !mainSymptoms.trim().isEmpty() &&
               preliminaryDiagnosis != null && !preliminaryDiagnosis.trim().isEmpty() &&
               medicalHistory != null && !medicalHistory.trim().isEmpty();
    }
    
    /**
     * 判断是否有检查结果
     */
    public boolean hasExamResults() {
        return examResults != null && !examResults.trim().isEmpty();
    }
    
    /**
     * 判断是否有治疗经过
     */
    public boolean hasTreatmentHistory() {
        return treatmentHistory != null && !treatmentHistory.trim().isEmpty();
    }
}
