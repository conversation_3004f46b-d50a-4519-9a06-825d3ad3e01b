package com.hys.hm.domain.referral.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

/**
 * 患者信息值对象
 * 转诊表单中的患者信息
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-29
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PatientInfo {
    
    /**
     * 患者ID
     */
    private String patientId;
    
    /**
     * 患者姓名
     */
    private String name;
    
    /**
     * 性别：1-男，2-女
     */
    private Integer gender;
    
    /**
     * 年龄
     */
    private Integer age;
    
    /**
     * 身份证号（加密存储）
     */
    private String idCard;
    
    /**
     * 联系电话（加密存储）
     */
    private String phone;
    
    /**
     * 地址
     */
    private String address;
    
    /**
     * 详细地址
     */
    private String addressDetail;
    
    /**
     * 档案编号
     */
    private String fileNumber;
    
    /**
     * 出生日期
     */
    private LocalDate birthDate;
    
    /**
     * 获取性别描述
     */
    public String getGenderDesc() {
        return gender != null && gender == 1 ? "男" : "女";
    }
    
    /**
     * 验证患者信息完整性
     */
    public void validate() {
        if (patientId == null || patientId.trim().isEmpty()) {
            throw new IllegalArgumentException("患者ID不能为空");
        }
        
        if (name == null || name.trim().isEmpty()) {
            throw new IllegalArgumentException("患者姓名不能为空");
        }
        
        if (gender == null || (gender != 1 && gender != 2)) {
            throw new IllegalArgumentException("性别必须为1（男）或2（女）");
        }
        
        if (age == null || age < 0 || age > 150) {
            throw new IllegalArgumentException("年龄必须在0-150之间");
        }
        
        if (phone == null || phone.trim().isEmpty()) {
            throw new IllegalArgumentException("联系电话不能为空");
        }
    }
}
