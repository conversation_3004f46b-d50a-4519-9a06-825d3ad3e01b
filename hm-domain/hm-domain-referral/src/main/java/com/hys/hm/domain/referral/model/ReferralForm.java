package com.hys.hm.domain.referral.model;

import com.hys.hm.shared.types.enums.ReferralStatus;
import com.hys.hm.shared.types.enums.UrgencyLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 转诊表单聚合根
 * 包含转诊的完整业务逻辑
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-29
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReferralForm {
    
    /**
     * 转诊ID
     */
    private String id;
    
    /**
     * 基础信息ID
     */
    private String basicInfoId;
    
    /**
     * 转诊编号
     */
    private String referralNo;
    
    /**
     * 转诊日期
     */
    private LocalDateTime referralDate;
    
    /**
     * 患者信息
     */
    private PatientInfo patientInfo;
    
    /**
     * 医疗信息
     */
    private MedicalInfo medicalInfo;
    
    /**
     * 转出医院信息
     */
    private HospitalInfo outHospital;
    
    /**
     * 转入医院信息
     */
    private HospitalInfo inHospital;
    
    /**
     * 转诊状态
     */
    @Builder.Default
    private ReferralStatus status = ReferralStatus.PENDING;
    
    /**
     * 拒绝原因
     */
    private String rejectReason;
    
    /**
     * 确认时间
     */
    private LocalDateTime confirmTime;
    
    /**
     * 紧急程度
     */
    @Builder.Default
    private UrgencyLevel urgencyLevel = UrgencyLevel.NORMAL;
    
    /**
     * 预约时间
     */
    private LocalDateTime appointmentTime;
    
    /**
     * 附件信息
     */
    private String attachments;
    
    /**
     * 备注信息
     */
    private String notes;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 创建人
     */
    private String createBy;
    
    /**
     * 更新人
     */
    private String updateBy;
    
    /**
     * 版本号（乐观锁）
     */
    private Long version;
    
    /**
     * 是否删除
     */
    @Builder.Default
    private Boolean deleted = false;
    
    // ========== 业务方法 ==========
    
    /**
     * 确认转诊
     */
    public void confirm(String operatorId) {
        if (!status.canConfirm()) {
            throw new IllegalStateException("当前状态不允许确认: " + status.getDescription());
        }
        
        this.status = ReferralStatus.CONFIRMED;
        this.confirmTime = LocalDateTime.now();
        this.rejectReason = null;
        this.updateBy = operatorId;
        this.updateTime = LocalDateTime.now();
    }
    
    /**
     * 拒绝转诊
     */
    public void reject(String reason, String operatorId) {
        if (!status.canReject()) {
            throw new IllegalStateException("当前状态不允许拒绝: " + status.getDescription());
        }
        
        if (reason == null || reason.trim().isEmpty()) {
            throw new IllegalArgumentException("拒绝原因不能为空");
        }
        
        this.status = ReferralStatus.REJECTED;
        this.rejectReason = reason;
        this.confirmTime = LocalDateTime.now();
        this.updateBy = operatorId;
        this.updateTime = LocalDateTime.now();
    }
    
    /**
     * 取消转诊
     */
    public void cancel(String operatorId) {
        if (!status.canCancel()) {
            throw new IllegalStateException("当前状态不允许取消: " + status.getDescription());
        }
        
        this.status = ReferralStatus.CANCELLED;
        this.updateBy = operatorId;
        this.updateTime = LocalDateTime.now();
    }
    
    /**
     * 完成转诊
     */
    public void complete(String operatorId) {
        if (!status.canComplete()) {
            throw new IllegalStateException("当前状态不允许完成: " + status.getDescription());
        }
        
        this.status = ReferralStatus.COMPLETED;
        this.updateBy = operatorId;
        this.updateTime = LocalDateTime.now();
    }
    
    // ========== 查询方法 ==========
    
    public boolean isPending() {
        return ReferralStatus.PENDING.equals(status);
    }
    
    public boolean isConfirmed() {
        return ReferralStatus.CONFIRMED.equals(status);
    }
    
    public boolean isUrgent() {
        return urgencyLevel != null && urgencyLevel.isUrgent();
    }
    
    public String getPatientName() {
        return patientInfo != null ? patientInfo.getName() : null;
    }
    
    public String getPatientId() {
        return patientInfo != null ? patientInfo.getPatientId() : null;
    }
    
    public String getReferralReason() {
        return medicalInfo != null ? medicalInfo.getReferralReason() : null;
    }
}
