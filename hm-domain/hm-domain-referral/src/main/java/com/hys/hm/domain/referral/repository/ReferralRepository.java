package com.hys.hm.domain.referral.repository;

import com.hys.hm.domain.referral.model.ReferralForm;
import com.hys.hm.shared.types.enums.ReferralStatus;
import com.hys.hm.shared.types.enums.UrgencyLevel;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 转诊仓储接口
 * 定义转诊聚合根的持久化操作
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-29
 */
public interface ReferralRepository {
    
    /**
     * 保存转诊表单
     */
    ReferralForm save(ReferralForm referralForm);
    
    /**
     * 根据ID查找转诊表单
     */
    Optional<ReferralForm> findById(String id);
    
    /**
     * 根据转诊编号查找转诊表单
     */
    Optional<ReferralForm> findByReferralNo(String referralNo);
    
    /**
     * 根据患者ID查找转诊表单列表
     */
    List<ReferralForm> findByPatientId(String patientId);
    
    /**
     * 根据状态查找转诊表单列表
     */
    List<ReferralForm> findByStatus(ReferralStatus status);
    
    /**
     * 根据转出医院查找转诊表单列表
     */
    List<ReferralForm> findByOutUnitId(String outUnitId);
    
    /**
     * 根据转入医院查找转诊表单列表
     */
    List<ReferralForm> findByInUnitId(String inUnitId);
    
    /**
     * 根据医生查找转诊表单列表（转出或转入）
     */
    List<ReferralForm> findByDoctorId(String doctorId);
    
    /**
     * 根据紧急程度查找转诊表单列表
     */
    List<ReferralForm> findByUrgencyLevel(UrgencyLevel urgencyLevel);
    
    /**
     * 查找待处理的转诊表单
     */
    List<ReferralForm> findPendingReferrals();
    
    /**
     * 查找紧急转诊表单
     */
    List<ReferralForm> findUrgentReferrals();
    
    /**
     * 查找超时的转诊表单
     */
    List<ReferralForm> findTimeoutReferrals(LocalDateTime cutoffTime);
    
    /**
     * 根据时间范围查找转诊表单
     */
    List<ReferralForm> findByDateRange(LocalDateTime startDate, LocalDateTime endDate);
    
    /**
     * 查找今日转诊表单
     */
    List<ReferralForm> findTodayReferrals();
    
    /**
     * 检查转诊编号是否存在
     */
    boolean existsByReferralNo(String referralNo);
    
    /**
     * 根据状态统计数量
     */
    long countByStatus(ReferralStatus status);
    
    /**
     * 根据医院统计数量
     */
    long countByUnitId(String unitId, boolean isOutUnit);
    
    /**
     * 统计今日转诊数量
     */
    long countTodayReferrals();
    
    /**
     * 根据转诊编号前缀查找最大编号
     */
    String findMaxReferralNoByPrefix(String prefix);
    
    /**
     * 软删除转诊表单
     */
    void softDelete(String id);
    
    /**
     * 批量保存转诊表单
     */
    List<ReferralForm> saveAll(List<ReferralForm> referralForms);
}
