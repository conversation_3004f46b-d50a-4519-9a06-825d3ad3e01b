package com.hys.hm.domain.referral.service;

import com.hys.hm.domain.referral.model.ReferralForm;
import com.hys.hm.domain.referral.repository.ReferralRepository;
import com.hys.hm.shared.events.EventPublisher;
import com.hys.hm.shared.events.referral.ReferralCreatedEvent;
import com.hys.hm.shared.events.referral.ReferralStatusChangedEvent;
import com.hys.hm.shared.types.dto.PatientBasicInfoDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * 转诊领域服务
 * 包含转诊相关的核心业务逻辑
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-29
 */
@Service
@Slf4j
@RequiredArgsConstructor
@Transactional
public class ReferralDomainService {
    
    private final ReferralRepository referralRepository;
    private final EventPublisher eventPublisher;
    
    /**
     * 创建转诊表单
     */
    public ReferralForm createReferralForm(ReferralForm referralForm, String operatorId) {
        log.info("创建转诊表单: patientId={}, operatorId={}", 
                referralForm.getPatientId(), operatorId);
        
        // 验证转诊表单
        validateReferralForm(referralForm);
        
        // 设置基础信息
        if (referralForm.getId() == null) {
            referralForm.setId(UUID.randomUUID().toString().replace("-", ""));
        }
        
        if (referralForm.getReferralNo() == null) {
            referralForm.setReferralNo(generateReferralNo());
        }
        
        if (referralForm.getReferralDate() == null) {
            referralForm.setReferralDate(LocalDateTime.now());
        }
        
        referralForm.setCreateTime(LocalDateTime.now());
        referralForm.setUpdateTime(LocalDateTime.now());
        referralForm.setCreateBy(operatorId);
        referralForm.setUpdateBy(operatorId);
        referralForm.setVersion(1L);
        
        // 保存转诊表单
        ReferralForm savedReferral = referralRepository.save(referralForm);
        
        // 发布转诊创建事件
        publishReferralCreatedEvent(savedReferral);
        
        log.info("转诊表单创建成功: id={}, referralNo={}", 
                savedReferral.getId(), savedReferral.getReferralNo());
        
        return savedReferral;
    }
    
    /**
     * 确认转诊
     */
    public ReferralForm confirmReferral(String referralId, String operatorId) {
        log.info("确认转诊: referralId={}, operatorId={}", referralId, operatorId);
        
        ReferralForm referral = getReferralById(referralId);
        
        // 记录原状态
        var oldStatus = referral.getStatus();
        
        // 执行确认操作
        referral.confirm(operatorId);
        
        // 保存更新
        ReferralForm updatedReferral = referralRepository.save(referral);
        
        // 发布状态变更事件
        publishStatusChangedEvent(updatedReferral, oldStatus, updatedReferral.getStatus(), null, operatorId);
        
        log.info("转诊确认成功: referralId={}, referralNo={}", 
                referralId, updatedReferral.getReferralNo());
        
        return updatedReferral;
    }
    
    /**
     * 拒绝转诊
     */
    public ReferralForm rejectReferral(String referralId, String reason, String operatorId) {
        log.info("拒绝转诊: referralId={}, reason={}, operatorId={}", 
                referralId, reason, operatorId);
        
        ReferralForm referral = getReferralById(referralId);
        
        // 记录原状态
        var oldStatus = referral.getStatus();
        
        // 执行拒绝操作
        referral.reject(reason, operatorId);
        
        // 保存更新
        ReferralForm updatedReferral = referralRepository.save(referral);
        
        // 发布状态变更事件
        publishStatusChangedEvent(updatedReferral, oldStatus, updatedReferral.getStatus(), reason, operatorId);
        
        log.info("转诊拒绝成功: referralId={}, referralNo={}", 
                referralId, updatedReferral.getReferralNo());
        
        return updatedReferral;
    }
    
    /**
     * 取消转诊
     */
    public ReferralForm cancelReferral(String referralId, String operatorId) {
        log.info("取消转诊: referralId={}, operatorId={}", referralId, operatorId);
        
        ReferralForm referral = getReferralById(referralId);
        
        // 记录原状态
        var oldStatus = referral.getStatus();
        
        // 执行取消操作
        referral.cancel(operatorId);
        
        // 保存更新
        ReferralForm updatedReferral = referralRepository.save(referral);
        
        // 发布状态变更事件
        publishStatusChangedEvent(updatedReferral, oldStatus, updatedReferral.getStatus(), null, operatorId);
        
        log.info("转诊取消成功: referralId={}, referralNo={}", 
                referralId, updatedReferral.getReferralNo());
        
        return updatedReferral;
    }
    
    /**
     * 完成转诊
     */
    public ReferralForm completeReferral(String referralId, String operatorId) {
        log.info("完成转诊: referralId={}, operatorId={}", referralId, operatorId);
        
        ReferralForm referral = getReferralById(referralId);
        
        // 记录原状态
        var oldStatus = referral.getStatus();
        
        // 执行完成操作
        referral.complete(operatorId);
        
        // 保存更新
        ReferralForm updatedReferral = referralRepository.save(referral);
        
        // 发布状态变更事件
        publishStatusChangedEvent(updatedReferral, oldStatus, updatedReferral.getStatus(), null, operatorId);
        
        log.info("转诊完成成功: referralId={}, referralNo={}", 
                referralId, updatedReferral.getReferralNo());
        
        return updatedReferral;
    }
    
    /**
     * 生成转诊编号
     */
    public String generateReferralNo() {
        String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String prefix = "ZZ" + dateStr;
        
        String maxNo = referralRepository.findMaxReferralNoByPrefix(prefix + "%");
        
        int sequence = 1;
        if (maxNo != null && maxNo.length() >= 14) {
            try {
                String sequenceStr = maxNo.substring(10);
                sequence = Integer.parseInt(sequenceStr) + 1;
            } catch (NumberFormatException e) {
                log.warn("解析转诊编号序号失败: {}", maxNo);
            }
        }
        
        return String.format("%s%04d", prefix, sequence);
    }
    
    /**
     * 批量确认转诊
     */
    public int batchConfirmReferrals(List<String> referralIds, String operatorId) {
        log.info("批量确认转诊: 数量={}, operatorId={}", referralIds.size(), operatorId);
        
        int successCount = 0;
        for (String referralId : referralIds) {
            try {
                confirmReferral(referralId, operatorId);
                successCount++;
            } catch (Exception e) {
                log.warn("批量确认转诊失败: referralId={}, error={}", referralId, e.getMessage());
            }
        }
        
        log.info("批量确认转诊完成: 总数={}, 成功={}", referralIds.size(), successCount);
        return successCount;
    }
    
    // ========== 私有方法 ==========
    
    private ReferralForm getReferralById(String referralId) {
        return referralRepository.findById(referralId)
                .orElseThrow(() -> new IllegalArgumentException("转诊表单不存在: " + referralId));
    }
    
    private void validateReferralForm(ReferralForm referralForm) {
        if (referralForm == null) {
            throw new IllegalArgumentException("转诊表单不能为空");
        }
        
        if (referralForm.getPatientInfo() != null) {
            referralForm.getPatientInfo().validate();
        }
        
        if (referralForm.getMedicalInfo() != null) {
            referralForm.getMedicalInfo().validate();
        }
        
        if (referralForm.getOutHospital() != null) {
            referralForm.getOutHospital().validate();
        }
        
        if (referralForm.getInHospital() != null) {
            referralForm.getInHospital().validate();
        }
    }
    
    private void publishReferralCreatedEvent(ReferralForm referral) {
        PatientBasicInfoDTO patientInfo = PatientBasicInfoDTO.builder()
                .patientId(referral.getPatientId())
                .name(referral.getPatientName())
                .gender(referral.getPatientInfo().getGender())
                .age(referral.getPatientInfo().getAge())
                .phone(referral.getPatientInfo().getPhone())
                .address(referral.getPatientInfo().getAddress())
                .build();
        
        ReferralCreatedEvent event = ReferralCreatedEvent.builder()
                .referralId(referral.getId())
                .referralNo(referral.getReferralNo())
                .patientInfo(patientInfo)
                .outUnitId(referral.getOutHospital().getUnitId())
                .outUnitName(referral.getOutHospital().getUnitName())
                .inUnitId(referral.getInHospital().getUnitId())
                .inUnitName(referral.getInHospital().getUnitName())
                .referralReason(referral.getReferralReason())
                .urgencyLevel(referral.getUrgencyLevel().getLevel())
                .createdBy(referral.getCreateBy())
                .build();
        
        eventPublisher.publishEvent(event);
    }
    
    private void publishStatusChangedEvent(ReferralForm referral, 
                                         com.hys.hm.shared.types.enums.ReferralStatus oldStatus,
                                         com.hys.hm.shared.types.enums.ReferralStatus newStatus,
                                         String reason, String operatorId) {
        ReferralStatusChangedEvent event = ReferralStatusChangedEvent.builder()
                .referralId(referral.getId())
                .referralNo(referral.getReferralNo())
                .patientId(referral.getPatientId())
                .patientName(referral.getPatientName())
                .oldStatus(oldStatus)
                .newStatus(newStatus)
                .reason(reason)
                .outUnitName(referral.getOutHospital().getUnitName())
                .inUnitName(referral.getInHospital().getUnitName())
                .operatedBy(operatorId)
                .build();
        
        eventPublisher.publishEvent(event);
    }
}
