package com.hys.hm.domain.referral.service;

import com.hys.hm.shared.types.dto.ReferralSummaryDTO;
import com.hys.hm.shared.types.enums.ReferralStatus;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 转诊查询服务接口
 * 供其他模块调用，获取转诊相关信息
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-29
 */
public interface ReferralQueryService {
    
    /**
     * 获取患者的转诊记录摘要
     */
    List<ReferralSummaryDTO> getPatientReferralSummary(String patientId);
    
    /**
     * 获取转诊详情
     */
    Optional<ReferralSummaryDTO> getReferralSummary(String referralId);
    
    /**
     * 根据转诊编号获取转诊摘要
     */
    Optional<ReferralSummaryDTO> getReferralSummaryByNo(String referralNo);
    
    /**
     * 获取医院的转诊统计
     */
    Map<String, Object> getHospitalReferralStatistics(String unitId);
    
    /**
     * 检查转诊是否存在
     */
    boolean existsReferral(String referralId);
    
    /**
     * 检查转诊编号是否存在
     */
    boolean existsReferralNo(String referralNo);
    
    /**
     * 获取患者最近的转诊记录
     */
    Optional<ReferralSummaryDTO> getLatestReferral(String patientId);
    
    /**
     * 根据状态获取转诊摘要列表
     */
    List<ReferralSummaryDTO> getReferralsByStatus(ReferralStatus status);
    
    /**
     * 获取待处理的转诊摘要列表
     */
    List<ReferralSummaryDTO> getPendingReferrals();
    
    /**
     * 获取紧急转诊摘要列表
     */
    List<ReferralSummaryDTO> getUrgentReferrals();
    
    /**
     * 获取今日转诊摘要列表
     */
    List<ReferralSummaryDTO> getTodayReferrals();
    
    /**
     * 根据状态统计转诊数量
     */
    long countByStatus(ReferralStatus status);
    
    /**
     * 统计今日转诊数量
     */
    long countTodayReferrals();
    
    /**
     * 统计待处理转诊数量
     */
    long countPendingReferrals();
    
    /**
     * 获取转诊统计信息
     */
    Map<String, Object> getReferralStatistics(String unitId, LocalDateTime startDate, LocalDateTime endDate);
}
