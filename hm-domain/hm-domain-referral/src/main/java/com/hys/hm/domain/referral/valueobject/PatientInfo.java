package com.hys.hm.domain.referral.valueobject;

import com.hys.hm.domain.referral.enums.Gender;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 患者信息值对象
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-23
 */
@Data
@EqualsAndHashCode
public class PatientInfo {
    
    /**
     * 患者姓名
     */
    private String name;
    
    /**
     * 性别
     */
    private Gender gender;
    
    /**
     * 年龄
     */
    private Integer age;
    
    /**
     * 身份证号
     */
    private String idCard;
    
    /**
     * 联系电话
     */
    private String phone;
    
    /**
     * 地址
     */
    private String address;
    
    /**
     * 详细地址
     */
    private String addressDetail;
    
    /**
     * 档案编号
     */
    private String fileNumber;
    
    /**
     * 创建患者信息
     */
    public static PatientInfo create(String name, Gender gender, Integer age, String idCard, String phone) {
        PatientInfo patientInfo = new PatientInfo();
        patientInfo.name = name;
        patientInfo.gender = gender;
        patientInfo.age = age;
        patientInfo.idCard = idCard;
        patientInfo.phone = phone;
        return patientInfo;
    }
    
    /**
     * 验证患者信息是否完整
     */
    public boolean isValid() {
        return name != null && !name.trim().isEmpty() &&
               gender != null &&
               age != null && age > 0 &&
               phone != null && !phone.trim().isEmpty();
    }
    
    /**
     * 获取完整地址
     */
    public String getFullAddress() {
        if (address == null) {
            return addressDetail;
        }
        if (addressDetail == null) {
            return address;
        }
        return address + " " + addressDetail;
    }
    
    /**
     * 脱敏显示身份证号
     */
    public String getMaskedIdCard() {
        if (idCard == null || idCard.length() < 8) {
            return idCard;
        }
        return idCard.substring(0, 4) + "****" + idCard.substring(idCard.length() - 4);
    }
    
    /**
     * 脱敏显示手机号
     */
    public String getMaskedPhone() {
        if (phone == null || phone.length() < 7) {
            return phone;
        }
        return phone.substring(0, 3) + "****" + phone.substring(phone.length() - 4);
    }
}
