package com.hys.hm.infrastructure.logging.entity;

import com.hys.hm.shared.logging.enums.BusinessStatus;
import com.hys.hm.shared.logging.enums.BusinessType;
import com.hys.hm.shared.logging.enums.OperatorType;
import com.hys.hm.shared.logging.enums.PlatformType;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.Comment;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;

/**
 * 系统日志实体
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-22
 */
@Entity
@Table(name = "sys_operation_log", indexes = {
        @Index(name = "idx_trace_id", columnList = "traceId"),
        @Index(name = "idx_user_name", columnList = "userName"),
        @Index(name = "idx_operate_time", columnList = "operateTime"),
        @Index(name = "idx_request_url", columnList = "requestUrl"),
        @Index(name = "idx_status", columnList = "status"),
        @Index(name = "idx_business_type", columnList = "businessType"),
        @Index(name = "idx_platform_type", columnList = "platformType")
})
@EntityListeners(AuditingEntityListener.class)
@Data
@EqualsAndHashCode(callSuper = false)
@Comment("系统操作日志表")
public class SystemLogEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Comment("日志主键")
    private Long id;

    @Column(length = 50, nullable = false)
    @Comment("追踪ID")
    private String traceId;

    @Column(length = 100)
    @Comment("模块标题")
    private String title;

    @Column(length = 10)
    @Comment("请求方式")
    private String requestMethod;

    @Column(length = 200)
    @Comment("方法名称")
    private String methodName;

    @Column(length = 500)
    @Comment("请求URL")
    private String requestUrl;

    @Column
    @Comment("是否项目日志")
    private Boolean projectRecordLog;

    @Column(length = 50)
    @Comment("主机地址")
    private String requestIp;

    @Enumerated(EnumType.STRING)
    @Column(length = 20)
    @Comment("操作类别")
    private OperatorType operatorType;

    @Enumerated(EnumType.STRING)
    @Column(length = 20)
    @Comment("业务类型")
    private BusinessType businessType;

    @Enumerated(EnumType.STRING)
    @Column(length = 20)
    @Comment("平台类型")
    private PlatformType platformType;

    @Column(length = 50)
    @Comment("操作人员登录名")
    private String userName;

    @Column(length = 50)
    @Comment("用户真实姓名")
    private String realName;

    @Column(length = 100)
    @Comment("操作地点")
    private String location;

    @Enumerated(EnumType.ORDINAL)
    @Column
    @Comment("操作状态")
    private BusinessStatus status;

    @Column(columnDefinition = "TEXT")
    @Comment("请求参数")
    private String requestParam;

    @Column(columnDefinition = "TEXT")
    @Comment("返回参数")
    private String responseResult;

    @Column(columnDefinition = "TEXT")
    @Comment("错误消息")
    private String errorMessage;

    @Column(nullable = false)
    @Comment("操作时间")
    private LocalDateTime operateTime;

    @Column
    @Comment("执行耗时（毫秒）")
    private Long executionTime;

    @Column(length = 500)
    @Comment("用户代理")
    private String userAgent;

    @Column(length = 200)
    @Comment("请求来源")
    private String referer;

    @Column(length = 50)
    @Comment("会话ID")
    private String sessionId;

    @CreatedDate
    @Column(updatable = false)
    @Comment("创建时间")
    private LocalDateTime createTime;

    @LastModifiedDate
    @Comment("更新时间")
    private LocalDateTime updateTime;
}
