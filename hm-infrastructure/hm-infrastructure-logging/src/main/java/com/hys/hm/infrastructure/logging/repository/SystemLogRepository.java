package com.hys.hm.infrastructure.logging.repository;

import com.hys.hm.infrastructure.logging.entity.SystemLogEntity;
import com.hys.hm.shared.logging.enums.BusinessStatus;
import com.hys.hm.shared.logging.enums.BusinessType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 系统日志数据访问层
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-22
 */
@Repository
public interface SystemLogRepository extends JpaRepository<SystemLogEntity, Long>, 
                                           JpaSpecificationExecutor<SystemLogEntity> {

    /**
     * 根据用户名查询日志
     */
    List<SystemLogEntity> findByUserNameOrderByOperateTimeDesc(String userName);

    /**
     * 根据时间范围查询日志
     */
    List<SystemLogEntity> findByOperateTimeBetweenOrderByOperateTimeDesc(
            LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 根据业务类型查询日志
     */
    List<SystemLogEntity> findByBusinessTypeOrderByOperateTimeDesc(BusinessType businessType);

    /**
     * 根据状态查询日志
     */
    List<SystemLogEntity> findByStatusOrderByOperateTimeDesc(BusinessStatus status);

    /**
     * 根据追踪ID查询日志
     */
    List<SystemLogEntity> findByTraceIdOrderByOperateTimeDesc(String traceId);

    /**
     * 分页查询用户操作日志
     */
    Page<SystemLogEntity> findByUserNameContainingOrderByOperateTimeDesc(
            String userName, Pageable pageable);

    /**
     * 查询指定时间之前的日志数量
     */
    @Query("SELECT COUNT(s) FROM SystemLogEntity s WHERE s.operateTime < :beforeTime")
    long countByOperateTimeBefore(@Param("beforeTime") LocalDateTime beforeTime);

    /**
     * 删除指定时间之前的日志
     */
    @Modifying
    @Query("DELETE FROM SystemLogEntity s WHERE s.operateTime < :beforeTime")
    int deleteByOperateTimeBefore(@Param("beforeTime") LocalDateTime beforeTime);

    /**
     * 查询最近的错误日志
     */
    @Query("SELECT s FROM SystemLogEntity s WHERE s.status = :status ORDER BY s.operateTime DESC")
    List<SystemLogEntity> findRecentErrorLogs(@Param("status") BusinessStatus status, Pageable pageable);

    /**
     * 统计指定时间范围内的操作次数
     */
    @Query("SELECT COUNT(s) FROM SystemLogEntity s WHERE s.operateTime BETWEEN :startTime AND :endTime")
    long countOperationsBetween(@Param("startTime") LocalDateTime startTime, 
                               @Param("endTime") LocalDateTime endTime);

    /**
     * 根据IP地址查询日志
     */
    List<SystemLogEntity> findByRequestIpOrderByOperateTimeDesc(String requestIp);

    /**
     * 查询执行时间超过指定阈值的日志
     */
    @Query("SELECT s FROM SystemLogEntity s WHERE s.executionTime > :threshold ORDER BY s.executionTime DESC")
    List<SystemLogEntity> findSlowOperations(@Param("threshold") Long threshold, Pageable pageable);
}
