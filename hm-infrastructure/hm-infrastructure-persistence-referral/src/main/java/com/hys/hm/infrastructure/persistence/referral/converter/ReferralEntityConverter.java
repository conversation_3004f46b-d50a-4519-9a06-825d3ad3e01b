package com.hys.hm.infrastructure.persistence.referral.converter;

import com.hys.hm.domain.referral.model.HospitalInfo;
import com.hys.hm.domain.referral.model.MedicalInfo;
import com.hys.hm.domain.referral.model.PatientInfo;
import com.hys.hm.domain.referral.model.ReferralForm;
import com.hys.hm.infrastructure.persistence.referral.entity.ReferralFormEntity;
import com.hys.hm.shared.types.enums.ReferralStatus;
import com.hys.hm.shared.types.enums.UrgencyLevel;
import org.springframework.stereotype.Component;

import java.time.LocalDate;

/**
 * 转诊实体转换器
 * 负责领域模型与持久化实体之间的转换
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-29
 */
@Component
public class ReferralEntityConverter {
    
    /**
     * 领域模型转换为持久化实体
     */
    public ReferralFormEntity toEntity(ReferralForm domain) {
        if (domain == null) {
            return null;
        }
        
        ReferralFormEntity entity = new ReferralFormEntity();
        
        // 基础信息
        entity.setId(domain.getId());
        entity.setBasicInfoId(domain.getBasicInfoId());
        entity.setReferralNo(domain.getReferralNo());
        entity.setReferralDate(domain.getReferralDate());
        
        // 患者信息
        if (domain.getPatientInfo() != null) {
            PatientInfo patientInfo = domain.getPatientInfo();
            entity.setPatientName(patientInfo.getName());
            entity.setGender(patientInfo.getGender());
            entity.setAge(patientInfo.getAge());
            entity.setIdCard(patientInfo.getIdCard());
            entity.setPhone(patientInfo.getPhone());
            entity.setAddress(patientInfo.getAddress());
            entity.setAddressDetail(patientInfo.getAddressDetail());
            entity.setFileNumber(patientInfo.getFileNumber());
        }
        
        // 医疗信息
        if (domain.getMedicalInfo() != null) {
            MedicalInfo medicalInfo = domain.getMedicalInfo();
            entity.setMainSymptoms(medicalInfo.getMainSymptoms());
            entity.setPreliminaryDiagnosis(medicalInfo.getPreliminaryDiagnosis());
            entity.setMedicalHistory(medicalInfo.getMedicalHistory());
            entity.setExamResults(medicalInfo.getExamResults());
            entity.setTreatmentHistory(medicalInfo.getTreatmentHistory());
            entity.setReferralReason(medicalInfo.getReferralReason());
            entity.setReferralPurpose(medicalInfo.getReferralPurpose());
        }
        
        // 转出医院信息
        if (domain.getOutHospital() != null) {
            HospitalInfo outHospital = domain.getOutHospital();
            entity.setOutUnitId(outHospital.getUnitId());
            entity.setOutUnitName(outHospital.getUnitName());
            entity.setOutDeptId(outHospital.getDeptId());
            entity.setOutDeptName(outHospital.getDeptName());
            entity.setOutDoctorId(outHospital.getDoctorId());
            entity.setOutDoctorName(outHospital.getDoctorName());
            entity.setOutDoctorPhone(outHospital.getDoctorPhone());
        }
        
        // 转入医院信息
        if (domain.getInHospital() != null) {
            HospitalInfo inHospital = domain.getInHospital();
            entity.setInUnitId(inHospital.getUnitId());
            entity.setInUnitName(inHospital.getUnitName());
            entity.setInDeptId(inHospital.getDeptId());
            entity.setInDeptName(inHospital.getDeptName());
            entity.setInDoctorId(inHospital.getDoctorId());
            entity.setInDoctorName(inHospital.getDoctorName());
            entity.setInDoctorPhone(inHospital.getDoctorPhone());
        }
        
        // 状态信息
        entity.setStatus(domain.getStatus() != null ? domain.getStatus().getCode() : 1);
        entity.setRejectReason(domain.getRejectReason());
        entity.setConfirmTime(domain.getConfirmTime());
        entity.setUrgencyLevel(domain.getUrgencyLevel() != null ? domain.getUrgencyLevel().getLevel() : 1);
        entity.setAppointmentTime(domain.getAppointmentTime());
        entity.setAttachments(domain.getAttachments());
        entity.setNotes(domain.getNotes());
        
        // 审计信息
        entity.setCreateTime(domain.getCreateTime());
        entity.setUpdateTime(domain.getUpdateTime());
        entity.setCreateBy(domain.getCreateBy());
        entity.setUpdateBy(domain.getUpdateBy());
        entity.setVersion(domain.getVersion());
        entity.setDeleted(domain.getDeleted());
        
        return entity;
    }
    
    /**
     * 持久化实体转换为领域模型
     */
    public ReferralForm toDomain(ReferralFormEntity entity) {
        if (entity == null) {
            return null;
        }
        
        // 构建患者信息
        PatientInfo patientInfo = PatientInfo.builder()
                .patientId(entity.getBasicInfoId())
                .name(entity.getPatientName())
                .gender(entity.getGender())
                .age(entity.getAge())
                .idCard(entity.getIdCard())
                .phone(entity.getPhone())
                .address(entity.getAddress())
                .addressDetail(entity.getAddressDetail())
                .fileNumber(entity.getFileNumber())
                .build();
        
        // 构建医疗信息
        MedicalInfo medicalInfo = MedicalInfo.builder()
                .mainSymptoms(entity.getMainSymptoms())
                .preliminaryDiagnosis(entity.getPreliminaryDiagnosis())
                .medicalHistory(entity.getMedicalHistory())
                .examResults(entity.getExamResults())
                .treatmentHistory(entity.getTreatmentHistory())
                .referralReason(entity.getReferralReason())
                .referralPurpose(entity.getReferralPurpose())
                .build();
        
        // 构建转出医院信息
        HospitalInfo outHospital = HospitalInfo.builder()
                .unitId(entity.getOutUnitId())
                .unitName(entity.getOutUnitName())
                .deptId(entity.getOutDeptId())
                .deptName(entity.getOutDeptName())
                .doctorId(entity.getOutDoctorId())
                .doctorName(entity.getOutDoctorName())
                .doctorPhone(entity.getOutDoctorPhone())
                .build();
        
        // 构建转入医院信息
        HospitalInfo inHospital = HospitalInfo.builder()
                .unitId(entity.getInUnitId())
                .unitName(entity.getInUnitName())
                .deptId(entity.getInDeptId())
                .deptName(entity.getInDeptName())
                .doctorId(entity.getInDoctorId())
                .doctorName(entity.getInDoctorName())
                .doctorPhone(entity.getInDoctorPhone())
                .build();
        
        // 构建转诊表单
        return ReferralForm.builder()
                .id(entity.getId())
                .basicInfoId(entity.getBasicInfoId())
                .referralNo(entity.getReferralNo())
                .referralDate(entity.getReferralDate())
                .patientInfo(patientInfo)
                .medicalInfo(medicalInfo)
                .outHospital(outHospital)
                .inHospital(inHospital)
                .status(ReferralStatus.fromCode(entity.getStatus()))
                .rejectReason(entity.getRejectReason())
                .confirmTime(entity.getConfirmTime())
                .urgencyLevel(UrgencyLevel.fromLevel(entity.getUrgencyLevel()))
                .appointmentTime(entity.getAppointmentTime())
                .attachments(entity.getAttachments())
                .notes(entity.getNotes())
                .createTime(entity.getCreateTime())
                .updateTime(entity.getUpdateTime())
                .createBy(entity.getCreateBy())
                .updateBy(entity.getUpdateBy())
                .version(entity.getVersion())
                .deleted(entity.getDeleted())
                .build();
    }
}
