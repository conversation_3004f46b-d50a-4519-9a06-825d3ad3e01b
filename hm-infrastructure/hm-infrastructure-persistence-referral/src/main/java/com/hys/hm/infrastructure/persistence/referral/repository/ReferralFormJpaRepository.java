package com.hys.hm.infrastructure.persistence.referral.repository;

import com.hys.hm.infrastructure.persistence.referral.entity.ReferralFormEntity;
import com.hys.hm.shared.framework.repository.BaseRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 转诊表单JPA仓储接口
 * 继承BaseRepository获得完整的CRUD和动态查询功能
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-29
 */
@Repository
public interface ReferralFormJpaRepository extends BaseRepository<ReferralFormEntity, String> {

    /**
     * 根据转诊编号查找转诊表单
     */
    Optional<ReferralFormEntity> findByReferralNo(String referralNo);

    /**
     * 根据基础信息ID查找转诊表单列表
     */
    List<ReferralFormEntity> findByBasicInfoId(String basicInfoId);

    /**
     * 根据转出医院查找转诊表单列表
     */
    List<ReferralFormEntity> findByOutUnitId(String outUnitId);

    /**
     * 根据转入医院查找转诊表单列表
     */
    List<ReferralFormEntity> findByInUnitId(String inUnitId);

    /**
     * 根据转出医生查找转诊表单列表
     */
    List<ReferralFormEntity> findByOutDoctorId(String outDoctorId);

    /**
     * 根据转入医生查找转诊表单列表
     */
    List<ReferralFormEntity> findByInDoctorId(String inDoctorId);

    /**
     * 根据状态查找转诊表单列表
     */
    List<ReferralFormEntity> findByStatus(Integer status);

    /**
     * 根据患者姓名模糊查找转诊表单列表
     */
    List<ReferralFormEntity> findByPatientNameContaining(String patientName);

    /**
     * 根据时间范围查找转诊表单列表
     */
    List<ReferralFormEntity> findByReferralDateBetween(LocalDateTime startDate, LocalDateTime endDate);

    /**
     * 根据状态统计转诊表单数量
     */
    long countByStatus(Integer status);

    /**
     * 根据转出医院统计转诊表单数量
     */
    long countByOutUnitId(String outUnitId);

    /**
     * 根据转入医院统计转诊表单数量
     */
    long countByInUnitId(String inUnitId);

    /**
     * 检查转诊编号是否存在
     */
    boolean existsByReferralNo(String referralNo);

    /**
     * 查找超时的转诊表单（超过指定时间未确认）
     */
    @Query("SELECT r FROM ReferralFormEntity r WHERE r.status = 1 AND r.referralDate < :cutoffTime AND r.deleted = 0")
    List<ReferralFormEntity> findTimeoutReferrals(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 查找待处理的转诊表单
     */
    @Query("SELECT r FROM ReferralFormEntity r WHERE r.status = 1 AND r.deleted = 0 ORDER BY r.referralDate ASC")
    List<ReferralFormEntity> findPendingReferrals();

    /**
     * 查找紧急转诊表单
     */
    @Query("SELECT r FROM ReferralFormEntity r WHERE r.urgencyLevel >= 2 AND r.deleted = 0 ORDER BY r.urgencyLevel DESC, r.referralDate ASC")
    List<ReferralFormEntity> findUrgentReferrals();

    /**
     * 根据医院和状态统计转诊表单数量
     */
    @Query("SELECT COUNT(r) FROM ReferralFormEntity r WHERE (r.outUnitId = :unitId OR r.inUnitId = :unitId) AND r.status = :status AND r.deleted = 0")
    long countByUnitIdAndStatus(@Param("unitId") String unitId, @Param("status") Integer status);

    /**
     * 根据医生查找转诊表单列表
     */
    @Query("SELECT r FROM ReferralFormEntity r WHERE (r.outDoctorId = :doctorId OR r.inDoctorId = :doctorId) AND r.deleted = 0 ORDER BY r.referralDate DESC")
    List<ReferralFormEntity> findByDoctorId(@Param("doctorId") String doctorId);

    /**
     * 根据时间范围和状态查找转诊表单列表
     */
    @Query("SELECT r FROM ReferralFormEntity r WHERE r.referralDate BETWEEN :startDate AND :endDate AND r.status = :status AND r.deleted = 0 ORDER BY r.referralDate DESC")
    List<ReferralFormEntity> findByDateRangeAndStatus(@Param("startDate") LocalDateTime startDate, 
                                                     @Param("endDate") LocalDateTime endDate, 
                                                     @Param("status") Integer status);

    /**
     * 根据紧急程度查找转诊表单列表
     */
    List<ReferralFormEntity> findByUrgencyLevel(Integer urgencyLevel);

    /**
     * 查找今日转诊表单
     */
    @Query("SELECT r FROM ReferralFormEntity r WHERE r.referralDate BETWEEN :startOfDay AND :endOfDay AND r.deleted = 0 ORDER BY r.referralDate DESC")
    List<ReferralFormEntity> findTodayReferrals(@Param("startOfDay") LocalDateTime startOfDay, 
                                               @Param("endOfDay") LocalDateTime endOfDay);

    /**
     * 统计今日转诊表单数量
     */
    @Query("SELECT COUNT(r) FROM ReferralFormEntity r WHERE r.referralDate BETWEEN :startOfDay AND :endOfDay AND r.deleted = 0")
    long countTodayReferrals(@Param("startOfDay") LocalDateTime startOfDay, 
                            @Param("endOfDay") LocalDateTime endOfDay);

    /**
     * 根据转诊编号前缀查找最大编号
     */
    @Query("SELECT MAX(r.referralNo) FROM ReferralFormEntity r WHERE r.referralNo LIKE :prefix AND r.deleted = 0")
    String findMaxReferralNoByPrefix(@Param("prefix") String prefix);

    /**
     * 根据身份证号查询转诊表单（支持加密字段模糊查询）
     */
    @Query("SELECT r FROM ReferralFormEntity r WHERE r.idCard LIKE %:idCardToken% AND r.deleted = 0")
    List<ReferralFormEntity> findByIdCardToken(@Param("idCardToken") String idCardToken);

    /**
     * 根据手机号查询转诊表单（支持加密字段模糊查询）
     */
    @Query("SELECT r FROM ReferralFormEntity r WHERE r.phone LIKE %:phoneToken% AND r.deleted = 0")
    List<ReferralFormEntity> findByPhoneToken(@Param("phoneToken") String phoneToken);
}
