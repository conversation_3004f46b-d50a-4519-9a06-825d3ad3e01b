package com.hys.hm.infrastructure.persistence.referral.repository;

import com.hys.hm.domain.referral.model.ReferralForm;
import com.hys.hm.domain.referral.repository.ReferralRepository;
import com.hys.hm.infrastructure.persistence.referral.converter.ReferralEntityConverter;
import com.hys.hm.infrastructure.persistence.referral.entity.ReferralFormEntity;
import com.hys.hm.shared.types.enums.ReferralStatus;
import com.hys.hm.shared.types.enums.UrgencyLevel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 转诊仓储实现类
 * 实现领域层定义的ReferralRepository接口
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-29
 */
@Repository
@Slf4j
@RequiredArgsConstructor
public class ReferralRepositoryImpl implements ReferralRepository {
    
    private final ReferralFormJpaRepository jpaRepository;
    private final ReferralEntityConverter converter;
    
    @Override
    public ReferralForm save(ReferralForm referralForm) {
        log.debug("保存转诊表单: id={}", referralForm.getId());
        
        ReferralFormEntity entity = converter.toEntity(referralForm);
        ReferralFormEntity savedEntity = jpaRepository.save(entity);
        
        log.debug("转诊表单保存成功: id={}", savedEntity.getId());
        return converter.toDomain(savedEntity);
    }
    
    @Override
    public Optional<ReferralForm> findById(String id) {
        log.debug("根据ID查找转诊表单: id={}", id);
        
        return jpaRepository.findById(id)
                .map(converter::toDomain);
    }
    
    @Override
    public Optional<ReferralForm> findByReferralNo(String referralNo) {
        log.debug("根据转诊编号查找转诊表单: referralNo={}", referralNo);
        
        return jpaRepository.findByReferralNo(referralNo)
                .map(converter::toDomain);
    }
    
    @Override
    public List<ReferralForm> findByPatientId(String patientId) {
        log.debug("根据患者ID查找转诊表单: patientId={}", patientId);
        
        return jpaRepository.findByBasicInfoId(patientId)
                .stream()
                .map(converter::toDomain)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<ReferralForm> findByStatus(ReferralStatus status) {
        log.debug("根据状态查找转诊表单: status={}", status);
        
        return jpaRepository.findByStatus(status.getCode())
                .stream()
                .map(converter::toDomain)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<ReferralForm> findByOutUnitId(String outUnitId) {
        log.debug("根据转出医院查找转诊表单: outUnitId={}", outUnitId);
        
        return jpaRepository.findByOutUnitId(outUnitId)
                .stream()
                .map(converter::toDomain)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<ReferralForm> findByInUnitId(String inUnitId) {
        log.debug("根据转入医院查找转诊表单: inUnitId={}", inUnitId);
        
        return jpaRepository.findByInUnitId(inUnitId)
                .stream()
                .map(converter::toDomain)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<ReferralForm> findByDoctorId(String doctorId) {
        log.debug("根据医生查找转诊表单: doctorId={}", doctorId);
        
        return jpaRepository.findByDoctorId(doctorId)
                .stream()
                .map(converter::toDomain)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<ReferralForm> findByUrgencyLevel(UrgencyLevel urgencyLevel) {
        log.debug("根据紧急程度查找转诊表单: urgencyLevel={}", urgencyLevel);
        
        return jpaRepository.findByUrgencyLevel(urgencyLevel.getLevel())
                .stream()
                .map(converter::toDomain)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<ReferralForm> findPendingReferrals() {
        log.debug("查找待处理的转诊表单");
        
        return jpaRepository.findPendingReferrals()
                .stream()
                .map(converter::toDomain)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<ReferralForm> findUrgentReferrals() {
        log.debug("查找紧急转诊表单");
        
        return jpaRepository.findUrgentReferrals()
                .stream()
                .map(converter::toDomain)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<ReferralForm> findTimeoutReferrals(LocalDateTime cutoffTime) {
        log.debug("查找超时转诊表单: cutoffTime={}", cutoffTime);
        
        return jpaRepository.findTimeoutReferrals(cutoffTime)
                .stream()
                .map(converter::toDomain)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<ReferralForm> findByDateRange(LocalDateTime startDate, LocalDateTime endDate) {
        log.debug("根据时间范围查找转诊表单: startDate={}, endDate={}", startDate, endDate);
        
        return jpaRepository.findByReferralDateBetween(startDate, endDate)
                .stream()
                .map(converter::toDomain)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<ReferralForm> findTodayReferrals() {
        log.debug("查找今日转诊表单");
        
        LocalDateTime startOfDay = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
        LocalDateTime endOfDay = startOfDay.plusDays(1).minusNanos(1);
        
        return jpaRepository.findTodayReferrals(startOfDay, endOfDay)
                .stream()
                .map(converter::toDomain)
                .collect(Collectors.toList());
    }
    
    @Override
    public boolean existsByReferralNo(String referralNo) {
        log.debug("检查转诊编号是否存在: referralNo={}", referralNo);
        
        return jpaRepository.existsByReferralNo(referralNo);
    }
    
    @Override
    public long countByStatus(ReferralStatus status) {
        log.debug("根据状态统计转诊数量: status={}", status);
        
        return jpaRepository.countByStatus(status.getCode());
    }
    
    @Override
    public long countByUnitId(String unitId, boolean isOutUnit) {
        log.debug("根据医院统计转诊数量: unitId={}, isOutUnit={}", unitId, isOutUnit);
        
        if (isOutUnit) {
            return jpaRepository.countByOutUnitId(unitId);
        } else {
            return jpaRepository.countByInUnitId(unitId);
        }
    }
    
    @Override
    public long countTodayReferrals() {
        log.debug("统计今日转诊数量");
        
        LocalDateTime startOfDay = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
        LocalDateTime endOfDay = startOfDay.plusDays(1).minusNanos(1);
        
        return jpaRepository.countTodayReferrals(startOfDay, endOfDay);
    }
    
    @Override
    public String findMaxReferralNoByPrefix(String prefix) {
        log.debug("根据前缀查找最大转诊编号: prefix={}", prefix);
        
        return jpaRepository.findMaxReferralNoByPrefix(prefix);
    }
    
    @Override
    public void softDelete(String id) {
        log.debug("软删除转诊表单: id={}", id);
        
        jpaRepository.softDeleteById(id);
    }
    
    @Override
    public List<ReferralForm> saveAll(List<ReferralForm> referralForms) {
        log.debug("批量保存转诊表单: 数量={}", referralForms.size());
        
        List<ReferralFormEntity> entities = referralForms.stream()
                .map(converter::toEntity)
                .collect(Collectors.toList());
        
        List<ReferralFormEntity> savedEntities = jpaRepository.saveAll(entities);
        
        return savedEntities.stream()
                .map(converter::toDomain)
                .collect(Collectors.toList());
    }
}
