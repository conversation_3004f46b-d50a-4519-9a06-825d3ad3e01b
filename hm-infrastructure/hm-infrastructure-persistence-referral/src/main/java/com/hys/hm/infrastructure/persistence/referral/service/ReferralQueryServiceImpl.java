package com.hys.hm.infrastructure.persistence.referral.service;

import com.hys.hm.domain.referral.service.ReferralQueryService;
import com.hys.hm.infrastructure.persistence.referral.entity.ReferralFormEntity;
import com.hys.hm.infrastructure.persistence.referral.repository.ReferralFormJpaRepository;
import com.hys.hm.shared.types.dto.ReferralSummaryDTO;
import com.hys.hm.shared.types.enums.ReferralStatus;
import com.hys.hm.shared.types.enums.UrgencyLevel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 转诊查询服务实现类
 * 实现转诊查询相关功能，供其他模块调用
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-29
 */
@Service
@Slf4j
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class ReferralQueryServiceImpl implements ReferralQueryService {
    
    private final ReferralFormJpaRepository jpaRepository;
    
    @Override
    public List<ReferralSummaryDTO> getPatientReferralSummary(String patientId) {
        log.debug("获取患者转诊记录摘要: patientId={}", patientId);
        
        List<ReferralFormEntity> entities = jpaRepository.findByBasicInfoId(patientId);
        
        return entities.stream()
                .map(this::convertToSummaryDTO)
                .collect(Collectors.toList());
    }
    
    @Override
    public Optional<ReferralSummaryDTO> getReferralSummary(String referralId) {
        log.debug("获取转诊摘要: referralId={}", referralId);
        
        return jpaRepository.findById(referralId)
                .map(this::convertToSummaryDTO);
    }
    
    @Override
    public Optional<ReferralSummaryDTO> getReferralSummaryByNo(String referralNo) {
        log.debug("根据转诊编号获取转诊摘要: referralNo={}", referralNo);
        
        return jpaRepository.findByReferralNo(referralNo)
                .map(this::convertToSummaryDTO);
    }
    
    @Override
    public Map<String, Object> getHospitalReferralStatistics(String unitId) {
        log.debug("获取医院转诊统计: unitId={}", unitId);
        
        Map<String, Object> statistics = new HashMap<>();
        
        // 转出统计
        long outCount = jpaRepository.countByOutUnitId(unitId);
        statistics.put("outCount", outCount);
        
        // 转入统计
        long inCount = jpaRepository.countByInUnitId(unitId);
        statistics.put("inCount", inCount);
        
        // 总计
        statistics.put("totalCount", outCount + inCount);
        
        // 按状态统计
        for (ReferralStatus status : ReferralStatus.values()) {
            long count = jpaRepository.countByUnitIdAndStatus(unitId, status.getCode());
            statistics.put(status.name().toLowerCase() + "Count", count);
        }
        
        return statistics;
    }
    
    @Override
    public boolean existsReferral(String referralId) {
        log.debug("检查转诊是否存在: referralId={}", referralId);
        
        return jpaRepository.existsById(referralId);
    }
    
    @Override
    public boolean existsReferralNo(String referralNo) {
        log.debug("检查转诊编号是否存在: referralNo={}", referralNo);
        
        return jpaRepository.existsByReferralNo(referralNo);
    }
    
    @Override
    public Optional<ReferralSummaryDTO> getLatestReferral(String patientId) {
        log.debug("获取患者最近的转诊记录: patientId={}", patientId);
        
        List<ReferralFormEntity> entities = jpaRepository.findByBasicInfoId(patientId);
        
        return entities.stream()
                .max((e1, e2) -> e1.getReferralDate().compareTo(e2.getReferralDate()))
                .map(this::convertToSummaryDTO);
    }
    
    @Override
    public List<ReferralSummaryDTO> getReferralsByStatus(ReferralStatus status) {
        log.debug("根据状态获取转诊摘要列表: status={}", status);
        
        List<ReferralFormEntity> entities = jpaRepository.findByStatus(status.getCode());
        
        return entities.stream()
                .map(this::convertToSummaryDTO)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<ReferralSummaryDTO> getPendingReferrals() {
        log.debug("获取待处理的转诊摘要列表");
        
        List<ReferralFormEntity> entities = jpaRepository.findPendingReferrals();
        
        return entities.stream()
                .map(this::convertToSummaryDTO)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<ReferralSummaryDTO> getUrgentReferrals() {
        log.debug("获取紧急转诊摘要列表");
        
        List<ReferralFormEntity> entities = jpaRepository.findUrgentReferrals();
        
        return entities.stream()
                .map(this::convertToSummaryDTO)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<ReferralSummaryDTO> getTodayReferrals() {
        log.debug("获取今日转诊摘要列表");
        
        LocalDateTime startOfDay = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
        LocalDateTime endOfDay = startOfDay.plusDays(1).minusNanos(1);
        
        List<ReferralFormEntity> entities = jpaRepository.findTodayReferrals(startOfDay, endOfDay);
        
        return entities.stream()
                .map(this::convertToSummaryDTO)
                .collect(Collectors.toList());
    }
    
    @Override
    public long countByStatus(ReferralStatus status) {
        log.debug("根据状态统计转诊数量: status={}", status);
        
        return jpaRepository.countByStatus(status.getCode());
    }
    
    @Override
    public long countTodayReferrals() {
        log.debug("统计今日转诊数量");
        
        LocalDateTime startOfDay = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
        LocalDateTime endOfDay = startOfDay.plusDays(1).minusNanos(1);
        
        return jpaRepository.countTodayReferrals(startOfDay, endOfDay);
    }
    
    @Override
    public long countPendingReferrals() {
        log.debug("统计待处理转诊数量");
        
        return countByStatus(ReferralStatus.PENDING);
    }
    
    @Override
    public Map<String, Object> getReferralStatistics(String unitId, LocalDateTime startDate, LocalDateTime endDate) {
        log.debug("获取转诊统计信息: unitId={}, startDate={}, endDate={}", unitId, startDate, endDate);
        
        Map<String, Object> statistics = new HashMap<>();
        
        // 基础统计
        statistics.put("totalCount", jpaRepository.count());
        statistics.put("pendingCount", countPendingReferrals());
        statistics.put("todayCount", countTodayReferrals());
        
        // 按状态统计
        for (ReferralStatus status : ReferralStatus.values()) {
            long count = countByStatus(status);
            statistics.put(status.name().toLowerCase() + "Count", count);
        }
        
        // 医院相关统计
        if (unitId != null && !unitId.trim().isEmpty()) {
            statistics.put("outUnitCount", jpaRepository.countByOutUnitId(unitId));
            statistics.put("inUnitCount", jpaRepository.countByInUnitId(unitId));
        }
        
        // 时间范围统计
        if (startDate != null && endDate != null) {
            List<ReferralFormEntity> rangeEntities = jpaRepository.findByReferralDateBetween(startDate, endDate);
            statistics.put("rangeCount", rangeEntities.size());
            
            Map<Integer, Long> statusCount = rangeEntities.stream()
                    .collect(Collectors.groupingBy(ReferralFormEntity::getStatus, Collectors.counting()));
            statistics.put("rangeStatusCount", statusCount);
        }
        
        return statistics;
    }
    
    /**
     * 转换实体为摘要DTO
     */
    private ReferralSummaryDTO convertToSummaryDTO(ReferralFormEntity entity) {
        return ReferralSummaryDTO.builder()
                .referralId(entity.getId())
                .referralNo(entity.getReferralNo())
                .patientId(entity.getBasicInfoId())
                .patientName(entity.getPatientName())
                .outUnitId(entity.getOutUnitId())
                .outUnitName(entity.getOutUnitName())
                .inUnitId(entity.getInUnitId())
                .inUnitName(entity.getInUnitName())
                .status(ReferralStatus.fromCode(entity.getStatus()))
                .referralDate(entity.getReferralDate())
                .urgencyLevel(UrgencyLevel.fromLevel(entity.getUrgencyLevel()))
                .referralReason(entity.getReferralReason())
                .confirmTime(entity.getConfirmTime())
                .createTime(entity.getCreateTime())
                .build();
    }
}
