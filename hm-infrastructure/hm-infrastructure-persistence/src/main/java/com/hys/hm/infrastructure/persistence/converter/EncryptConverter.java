package com.hys.hm.infrastructure.persistence.converter;

import com.hys.hm.shared.common.annotation.EncryptField;
import com.hys.hm.shared.common.service.EncryptService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;

/**
 * JPA加密字段转换器
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-23
 */
@Slf4j
@Component
@Converter
public class EncryptConverter implements AttributeConverter<String, String> {
    
    private static EncryptService encryptService;
    
    @Autowired
    public void setEncryptService(EncryptService encryptService) {
        EncryptConverter.encryptService = encryptService;
    }
    
    @Override
    public String convertToDatabaseColumn(String attribute) {
        if (attribute == null || attribute.isEmpty()) {
            return attribute;
        }
        
        try {
            // 默认使用AES加密
            return encryptService.encrypt(attribute, EncryptField.EncryptType.AES);
        } catch (Exception e) {
            log.error("数据库字段加密失败: {}", e.getMessage(), e);
            throw new RuntimeException("数据库字段加密失败", e);
        }
    }
    
    @Override
    public String convertToEntityAttribute(String dbData) {
        if (dbData == null || dbData.isEmpty()) {
            return dbData;
        }
        
        try {
            // 默认使用AES解密
            return encryptService.decrypt(dbData, EncryptField.EncryptType.AES);
        } catch (Exception e) {
            log.error("数据库字段解密失败: {}", e.getMessage(), e);
            // 解密失败时返回原值，避免系统崩溃
            return dbData;
        }
    }
}
