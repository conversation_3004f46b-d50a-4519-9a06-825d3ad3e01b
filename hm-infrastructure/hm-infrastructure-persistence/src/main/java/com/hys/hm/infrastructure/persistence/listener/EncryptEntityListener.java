package com.hys.hm.infrastructure.persistence.listener;

import com.hys.hm.infrastructure.persistence.service.EncryptFieldService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.persistence.*;
import java.lang.reflect.Field;

/**
 * JPA实体监听器
 * 自动处理加密字段的搜索索引
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-23
 */
@Slf4j
@Component
public class EncryptEntityListener {
    
    private static EncryptFieldService encryptFieldService;
    
    @Autowired
    public void setEncryptFieldService(EncryptFieldService encryptFieldService) {
        EncryptEntityListener.encryptFieldService = encryptFieldService;
    }
    
    /**
     * 实体保存后处理
     */
    @PostPersist
    public void postPersist(Object entity) {
        handleEntityChange(entity, "INSERT");
    }
    
    /**
     * 实体更新后处理
     */
    @PostUpdate
    public void postUpdate(Object entity) {
        handleEntityChange(entity, "UPDATE");
    }
    
    /**
     * 实体删除后处理
     */
    @PostRemove
    public void postRemove(Object entity) {
        handleEntityChange(entity, "DELETE");
    }
    
    /**
     * 处理实体变更
     */
    private void handleEntityChange(Object entity, String operation) {
        if (entity == null || encryptFieldService == null) {
            return;
        }
        
        try {
            String entityId = getEntityId(entity);
            if (entityId == null) {
                log.warn("无法获取实体ID，跳过加密索引处理: {}", entity.getClass().getSimpleName());
                return;
            }
            
            String entityType = entity.getClass().getSimpleName();
            
            switch (operation) {
                case "INSERT":
                case "UPDATE":
                    encryptFieldService.createSearchIndex(entity, entityId);
                    log.debug("更新加密字段索引: entity={}, id={}, operation={}", entityType, entityId, operation);
                    break;
                case "DELETE":
                    encryptFieldService.deleteSearchIndex(entityType, entityId);
                    log.debug("删除加密字段索引: entity={}, id={}, operation={}", entityType, entityId, operation);
                    break;
            }
        } catch (Exception e) {
            log.error("处理加密字段索引失败: entity={}, operation={}, error={}", 
                     entity.getClass().getSimpleName(), operation, e.getMessage(), e);
        }
    }
    
    /**
     * 获取实体ID
     */
    private String getEntityId(Object entity) {
        try {
            // 尝试获取标注了@Id的字段
            Field[] fields = entity.getClass().getDeclaredFields();
            for (Field field : fields) {
                if (field.isAnnotationPresent(Id.class)) {
                    field.setAccessible(true);
                    Object value = field.get(entity);
                    return value != null ? value.toString() : null;
                }
            }
            
            // 如果没有找到@Id注解，尝试查找名为"id"的字段
            try {
                Field idField = entity.getClass().getDeclaredField("id");
                idField.setAccessible(true);
                Object value = idField.get(entity);
                return value != null ? value.toString() : null;
            } catch (NoSuchFieldException e) {
                // 忽略，继续尝试其他方法
            }
            
            return null;
        } catch (Exception e) {
            log.error("获取实体ID失败: {}", e.getMessage(), e);
            return null;
        }
    }
}
