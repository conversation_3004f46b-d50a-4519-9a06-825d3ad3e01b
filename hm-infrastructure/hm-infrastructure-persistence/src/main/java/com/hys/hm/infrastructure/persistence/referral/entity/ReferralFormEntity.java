package com.hys.hm.infrastructure.persistence.referral.entity;

import com.hys.hm.shared.common.annotation.EncryptField;
import com.hys.hm.infrastructure.persistence.converter.EncryptConverter;
import com.hys.hm.infrastructure.persistence.listener.EncryptEntityListener;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.Comment;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;

/**
 * 转诊表单持久化实体
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-23
 */
@Entity
@Table(name = "DC_REFERRAL_FORM", indexes = {
        @Index(name = "idx_referral_no", columnList = "referralNo"),
        @Index(name = "idx_basic_info_id", columnList = "basicInfoId"),
        @Index(name = "idx_out_unit_id", columnList = "outUnitId"),
        @Index(name = "idx_in_unit_id", columnList = "inUnitId"),
        @Index(name = "idx_out_doctor_id", columnList = "outDoctorId"),
        @Index(name = "idx_in_doctor_id", columnList = "inDoctorId"),
        @Index(name = "idx_status", columnList = "status"),
        @Index(name = "idx_referral_date", columnList = "referralDate"),
        @Index(name = "idx_patient_name", columnList = "patientName")
})
@EntityListeners({AuditingEntityListener.class, EncryptEntityListener.class})
@Data
@EqualsAndHashCode(callSuper = false)
@Comment("转诊表单表")
public class ReferralFormEntity {

    @Id
    @Column(length = 32)
    @Comment("转诊表单ID")
    private String id;

    @Column(length = 32, nullable = false)
    @Comment("基础信息ID")
    private String basicInfoId;

    @Column(length = 20, nullable = false, unique = true)
    @Comment("转诊编号")
    private String referralNo;

    @Column(nullable = false)
    @Comment("转诊日期")
    private LocalDateTime referralDate;

    // ========== 患者信息 ==========

    @Column(length = 50, nullable = false)
    @Comment("患者姓名")
    private String patientName;

    @Column(nullable = false)
    @Comment("性别")
    private Integer gender;

    @Column(nullable = false)
    @Comment("年龄")
    private Integer age;

    @EncryptField(fuzzySearch = true, tokenLength = 4, description = "身份证号")
    // @Convert(converter = EncryptConverter.class)  // 暂时禁用，等迁移完成后启用
    @Column(length = 500)  // 加密后长度会增加
    @Comment("身份证号（准备加密存储）")
    private String idCard;

    @EncryptField(fuzzySearch = true, tokenLength = 3, description = "联系电话")
    // @Convert(converter = EncryptConverter.class)  // 暂时禁用，等迁移完成后启用
    @Column(length = 500, nullable = false)  // 加密后长度会增加
    @Comment("联系电话（准备加密存储）")
    private String phone;

    @Column(length = 200)
    @Comment("地址")
    private String address;

    @Column(length = 200)
    @Comment("详细地址")
    private String addressDetail;

    @Column(length = 50)
    @Comment("档案编号")
    private String fileNumber;

    // ========== 医疗信息 ==========

    @Column(length = 500, nullable = false)
    @Comment("初步诊断")
    private String impression;

    @Column(columnDefinition = "TEXT", nullable = false)
    @Comment("主要病史")
    private String mainHistory;

    @Column(columnDefinition = "TEXT")
    @Comment("既往病史")
    private String pastHistory;

    @Column(columnDefinition = "TEXT")
    @Comment("治疗过程")
    private String treatmentProcess;

    // ========== 转出医院信息 ==========

    @Column(length = 32, nullable = false)
    @Comment("转出医院ID")
    private String outUnitId;

    @Column(length = 100, nullable = false)
    @Comment("转出医院名称")
    private String outUnitName;

    @Column(length = 32)
    @Comment("转出科室ID")
    private String outDeptId;

    @Column(length = 50)
    @Comment("转出科室名称")
    private String outDeptName;

    @Column(length = 32, nullable = false)
    @Comment("转出医生ID")
    private String outDoctorId;

    @Column(length = 50, nullable = false)
    @Comment("转出医生姓名")
    private String outDoctorName;

    @Column(length = 20)
    @Comment("转出医生电话")
    private String outDoctorPhone;

    // ========== 转入医院信息 ==========

    @Column(length = 32, nullable = false)
    @Comment("转入医院ID")
    private String inUnitId;

    @Column(length = 100, nullable = false)
    @Comment("转入医院名称")
    private String inUnitName;

    @Column(length = 32)
    @Comment("转入科室ID")
    private String inDeptId;

    @Column(length = 50)
    @Comment("转入科室名称")
    private String inDeptName;

    @Column(length = 32)
    @Comment("转入医生ID")
    private String inDoctorId;

    @Column(length = 50)
    @Comment("转入医生姓名")
    private String inDoctorName;

    @Column(length = 20)
    @Comment("转入医生电话")
    private String inDoctorPhone;

    // ========== 状态信息 ==========

    @Column(nullable = false)
    @Comment("转诊状态")
    private Integer status;

    @Column(length = 500)
    @Comment("拒绝原因")
    private String rejectReason;

    @Column
    @Comment("确认时间")
    private LocalDateTime confirmTime;

    @CreatedDate
    @Column(updatable = false)
    @Comment("创建时间")
    private LocalDateTime createTime;

    @LastModifiedDate
    @Comment("更新时间")
    private LocalDateTime updateTime;
}
