package com.hys.hm.infrastructure.persistence.referral.entity;

import com.hys.hm.shared.common.annotation.EncryptField;
import com.hys.hm.shared.framework.base.BaseEntity;
import com.hys.hm.infrastructure.persistence.converter.EncryptConverter;
import com.hys.hm.infrastructure.persistence.listener.EncryptEntityListener;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.Comment;

import java.time.LocalDateTime;

/**
 * 转诊表单实体类（使用新框架）
 * 继承BaseEntity获得通用字段和审计功能
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-29
 */
@Entity
@Table(name = "referral_form", indexes = {
    @Index(name = "idx_referral_no", columnList = "referralNo", unique = true),
    @Index(name = "idx_basic_info_id", columnList = "basicInfoId"),
    @Index(name = "idx_status", columnList = "status"),
    @Index(name = "idx_referral_date", columnList = "referralDate"),
    @Index(name = "idx_out_unit_id", columnList = "outUnitId"),
    @Index(name = "idx_in_unit_id", columnList = "inUnitId"),
    @Index(name = "idx_patient_name", columnList = "patientName")
})
@EntityListeners({EncryptEntityListener.class})
@Data
@EqualsAndHashCode(callSuper = true)
@Comment("转诊表单表")
public class ReferralFormEntityNew extends BaseEntity<String> {

    /**
     * 主键ID
     */
    @Id
    @Column(length = 32)
    @Comment("转诊表单ID")
    private String id;

    /**
     * 基础信息ID
     */
    @Column(length = 32, nullable = false)
    @Comment("基础信息ID")
    private String basicInfoId;

    /**
     * 转诊编号
     */
    @Column(length = 20, nullable = false, unique = true)
    @Comment("转诊编号")
    private String referralNo;

    /**
     * 转诊日期
     */
    @Column(nullable = false)
    @Comment("转诊日期")
    private LocalDateTime referralDate;

    // ========== 患者信息 ==========

    /**
     * 患者姓名
     */
    @Column(length = 50, nullable = false)
    @Comment("患者姓名")
    private String patientName;

    /**
     * 性别
     */
    @Column(nullable = false)
    @Comment("性别：1-男，2-女")
    private Integer gender;

    /**
     * 年龄
     */
    @Column(nullable = false)
    @Comment("年龄")
    private Integer age;

    /**
     * 身份证号（加密存储）
     */
    @EncryptField(fuzzySearch = true, tokenLength = 4, description = "身份证号")
    @Convert(converter = EncryptConverter.class)
    @Column(length = 500)
    @Comment("身份证号（加密存储）")
    private String idCard;

    /**
     * 联系电话（加密存储）
     */
    @EncryptField(fuzzySearch = true, tokenLength = 3, description = "联系电话")
    @Convert(converter = EncryptConverter.class)
    @Column(length = 500, nullable = false)
    @Comment("联系电话（加密存储）")
    private String phone;

    /**
     * 地址
     */
    @Column(length = 200)
    @Comment("地址")
    private String address;

    /**
     * 详细地址
     */
    @Column(length = 200)
    @Comment("详细地址")
    private String addressDetail;

    /**
     * 档案编号
     */
    @Column(length = 50)
    @Comment("档案编号")
    private String fileNumber;

    // ========== 医疗信息 ==========

    /**
     * 主要症状
     */
    @Column(length = 1000)
    @Comment("主要症状")
    private String mainSymptoms;

    /**
     * 初步诊断
     */
    @Column(length = 500)
    @Comment("初步诊断")
    private String preliminaryDiagnosis;

    /**
     * 病史摘要
     */
    @Column(length = 2000)
    @Comment("病史摘要")
    private String medicalHistory;

    /**
     * 检查结果
     */
    @Column(length = 2000)
    @Comment("检查结果")
    private String examResults;

    /**
     * 治疗经过
     */
    @Column(length = 2000)
    @Comment("治疗经过")
    private String treatmentHistory;

    /**
     * 转诊原因
     */
    @Column(length = 1000, nullable = false)
    @Comment("转诊原因")
    private String referralReason;

    /**
     * 转诊目的
     */
    @Column(length = 500)
    @Comment("转诊目的")
    private String referralPurpose;

    // ========== 转出医院信息 ==========

    /**
     * 转出医院ID
     */
    @Column(length = 32, nullable = false)
    @Comment("转出医院ID")
    private String outUnitId;

    /**
     * 转出医院名称
     */
    @Column(length = 200, nullable = false)
    @Comment("转出医院名称")
    private String outUnitName;

    /**
     * 转出科室ID
     */
    @Column(length = 32)
    @Comment("转出科室ID")
    private String outDeptId;

    /**
     * 转出科室名称
     */
    @Column(length = 100)
    @Comment("转出科室名称")
    private String outDeptName;

    /**
     * 转出医生ID
     */
    @Column(length = 32)
    @Comment("转出医生ID")
    private String outDoctorId;

    /**
     * 转出医生姓名
     */
    @Column(length = 50)
    @Comment("转出医生姓名")
    private String outDoctorName;

    /**
     * 转出医生联系方式
     */
    @Column(length = 50)
    @Comment("转出医生联系方式")
    private String outDoctorPhone;

    // ========== 转入医院信息 ==========

    /**
     * 转入医院ID
     */
    @Column(length = 32, nullable = false)
    @Comment("转入医院ID")
    private String inUnitId;

    /**
     * 转入医院名称
     */
    @Column(length = 200, nullable = false)
    @Comment("转入医院名称")
    private String inUnitName;

    /**
     * 转入科室ID
     */
    @Column(length = 32)
    @Comment("转入科室ID")
    private String inDeptId;

    /**
     * 转入科室名称
     */
    @Column(length = 100)
    @Comment("转入科室名称")
    private String inDeptName;

    /**
     * 转入医生ID
     */
    @Column(length = 32)
    @Comment("转入医生ID")
    private String inDoctorId;

    /**
     * 转入医生姓名
     */
    @Column(length = 50)
    @Comment("转入医生姓名")
    private String inDoctorName;

    /**
     * 转入医生联系方式
     */
    @Column(length = 50)
    @Comment("转入医生联系方式")
    private String inDoctorPhone;

    // ========== 转诊状态信息 ==========

    /**
     * 转诊状态
     * 1-待处理，2-已确认，3-已拒绝，4-已取消，5-已完成
     */
    @Column(nullable = false)
    @Comment("转诊状态：1-待处理，2-已确认，3-已拒绝，4-已取消，5-已完成")
    private Integer status = 1;

    /**
     * 拒绝原因
     */
    @Column(length = 1000)
    @Comment("拒绝原因")
    private String rejectReason;

    /**
     * 确认时间
     */
    @Column
    @Comment("确认时间")
    private LocalDateTime confirmTime;

    /**
     * 紧急程度
     * 1-普通，2-紧急，3-急诊
     */
    @Column
    @Comment("紧急程度：1-普通，2-紧急，3-急诊")
    private Integer urgencyLevel = 1;

    /**
     * 预约时间
     */
    @Column
    @Comment("预约时间")
    private LocalDateTime appointmentTime;

    /**
     * 附件信息（JSON格式）
     */
    @Column(length = 2000)
    @Comment("附件信息（JSON格式）")
    private String attachments;

    /**
     * 备注信息
     */
    @Column(length = 1000)
    @Comment("备注信息")
    private String notes;

    @Override
    public String getId() {
        return id;
    }

    @Override
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 判断是否为待处理状态
     */
    public boolean isPending() {
        return status != null && status == 1;
    }

    /**
     * 判断是否为已确认状态
     */
    public boolean isConfirmed() {
        return status != null && status == 2;
    }

    /**
     * 判断是否为已拒绝状态
     */
    public boolean isRejected() {
        return status != null && status == 3;
    }

    /**
     * 判断是否为已取消状态
     */
    public boolean isCancelled() {
        return status != null && status == 4;
    }

    /**
     * 判断是否为已完成状态
     */
    public boolean isCompleted() {
        return status != null && status == 5;
    }

    /**
     * 判断是否为紧急转诊
     */
    public boolean isUrgent() {
        return urgencyLevel != null && urgencyLevel >= 2;
    }

    /**
     * 判断是否为急诊转诊
     */
    public boolean isEmergency() {
        return urgencyLevel != null && urgencyLevel == 3;
    }
}
