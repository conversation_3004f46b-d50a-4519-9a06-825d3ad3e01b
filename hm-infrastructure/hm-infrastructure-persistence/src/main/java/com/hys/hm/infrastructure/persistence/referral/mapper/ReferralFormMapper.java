package com.hys.hm.infrastructure.persistence.referral.mapper;

import com.hys.hm.domain.referral.model.ReferralForm;
import com.hys.hm.domain.referral.enums.ReferralStatus;
import com.hys.hm.domain.referral.enums.Gender;
import com.hys.hm.domain.referral.valueobject.PatientInfo;
import com.hys.hm.domain.referral.valueobject.MedicalInfo;
import com.hys.hm.domain.referral.valueobject.HospitalInfo;
import com.hys.hm.infrastructure.persistence.referral.entity.ReferralFormEntity;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 转诊表单数据映射器
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-23
 */
@Component
public class ReferralFormMapper {
    
    /**
     * 领域模型转换为持久化实体
     */
    public ReferralFormEntity toEntity(ReferralForm referralForm) {
        if (referralForm == null) {
            return null;
        }
        
        ReferralFormEntity entity = new ReferralFormEntity();
        
        // 基本信息
        entity.setId(referralForm.getId());
        entity.setBasicInfoId(referralForm.getBasicInfoId());
        entity.setReferralNo(referralForm.getReferralNo());
        entity.setReferralDate(referralForm.getReferralDate());
        
        // 患者信息
        if (referralForm.getPatientInfo() != null) {
            PatientInfo patientInfo = referralForm.getPatientInfo();
            entity.setPatientName(patientInfo.getName());
            entity.setGender(patientInfo.getGender() != null ? patientInfo.getGender().getCode() : null);
            entity.setAge(patientInfo.getAge());
            entity.setIdCard(patientInfo.getIdCard());
            entity.setPhone(patientInfo.getPhone());
            entity.setAddress(patientInfo.getAddress());
            entity.setAddressDetail(patientInfo.getAddressDetail());
            entity.setFileNumber(patientInfo.getFileNumber());
        }
        
        // 医疗信息
        if (referralForm.getMedicalInfo() != null) {
            MedicalInfo medicalInfo = referralForm.getMedicalInfo();
            entity.setImpression(medicalInfo.getImpression());
            entity.setMainHistory(medicalInfo.getMainHistory());
            entity.setPastHistory(medicalInfo.getPastHistory());
            entity.setTreatmentProcess(medicalInfo.getTreatmentProcess());
        }
        
        // 转出医院信息
        if (referralForm.getOutHospitalInfo() != null) {
            HospitalInfo outHospitalInfo = referralForm.getOutHospitalInfo();
            entity.setOutUnitId(outHospitalInfo.getUnitId());
            entity.setOutUnitName(outHospitalInfo.getUnitName());
            entity.setOutDeptId(outHospitalInfo.getDeptId());
            entity.setOutDeptName(outHospitalInfo.getDeptName());
            entity.setOutDoctorId(outHospitalInfo.getDoctorId());
            entity.setOutDoctorName(outHospitalInfo.getDoctorName());
            entity.setOutDoctorPhone(outHospitalInfo.getDoctorPhone());
        }
        
        // 转入医院信息
        if (referralForm.getInHospitalInfo() != null) {
            HospitalInfo inHospitalInfo = referralForm.getInHospitalInfo();
            entity.setInUnitId(inHospitalInfo.getUnitId());
            entity.setInUnitName(inHospitalInfo.getUnitName());
            entity.setInDeptId(inHospitalInfo.getDeptId());
            entity.setInDeptName(inHospitalInfo.getDeptName());
            entity.setInDoctorId(inHospitalInfo.getDoctorId());
            entity.setInDoctorName(inHospitalInfo.getDoctorName());
            entity.setInDoctorPhone(inHospitalInfo.getDoctorPhone());
        }
        
        // 状态信息
        entity.setStatus(referralForm.getStatus() != null ? referralForm.getStatus().getCode() : null);
        entity.setRejectReason(referralForm.getRejectReason());
        entity.setConfirmTime(referralForm.getConfirmTime());
        entity.setCreateTime(referralForm.getCreateTime());
        
        return entity;
    }
    
    /**
     * 持久化实体转换为领域模型
     */
    public ReferralForm toDomain(ReferralFormEntity entity) {
        if (entity == null) {
            return null;
        }
        
        ReferralForm referralForm = new ReferralForm();
        
        // 基本信息
        referralForm.setId(entity.getId());
        referralForm.setBasicInfoId(entity.getBasicInfoId());
        referralForm.setReferralNo(entity.getReferralNo());
        referralForm.setReferralDate(entity.getReferralDate());
        
        // 患者信息
        PatientInfo patientInfo = new PatientInfo();
        patientInfo.setName(entity.getPatientName());
        patientInfo.setGender(Gender.fromCode(entity.getGender()));
        patientInfo.setAge(entity.getAge());
        patientInfo.setIdCard(entity.getIdCard());
        patientInfo.setPhone(entity.getPhone());
        patientInfo.setAddress(entity.getAddress());
        patientInfo.setAddressDetail(entity.getAddressDetail());
        patientInfo.setFileNumber(entity.getFileNumber());
        referralForm.setPatientInfo(patientInfo);
        
        // 医疗信息
        MedicalInfo medicalInfo = new MedicalInfo();
        medicalInfo.setImpression(entity.getImpression());
        medicalInfo.setMainHistory(entity.getMainHistory());
        medicalInfo.setPastHistory(entity.getPastHistory());
        medicalInfo.setTreatmentProcess(entity.getTreatmentProcess());
        referralForm.setMedicalInfo(medicalInfo);
        
        // 转出医院信息
        HospitalInfo outHospitalInfo = new HospitalInfo();
        outHospitalInfo.setUnitId(entity.getOutUnitId());
        outHospitalInfo.setUnitName(entity.getOutUnitName());
        outHospitalInfo.setDeptId(entity.getOutDeptId());
        outHospitalInfo.setDeptName(entity.getOutDeptName());
        outHospitalInfo.setDoctorId(entity.getOutDoctorId());
        outHospitalInfo.setDoctorName(entity.getOutDoctorName());
        outHospitalInfo.setDoctorPhone(entity.getOutDoctorPhone());
        referralForm.setOutHospitalInfo(outHospitalInfo);
        
        // 转入医院信息
        HospitalInfo inHospitalInfo = new HospitalInfo();
        inHospitalInfo.setUnitId(entity.getInUnitId());
        inHospitalInfo.setUnitName(entity.getInUnitName());
        inHospitalInfo.setDeptId(entity.getInDeptId());
        inHospitalInfo.setDeptName(entity.getInDeptName());
        inHospitalInfo.setDoctorId(entity.getInDoctorId());
        inHospitalInfo.setDoctorName(entity.getInDoctorName());
        inHospitalInfo.setDoctorPhone(entity.getInDoctorPhone());
        referralForm.setInHospitalInfo(inHospitalInfo);
        
        // 状态信息
        referralForm.setStatus(ReferralStatus.fromCode(entity.getStatus()));
        referralForm.setRejectReason(entity.getRejectReason());
        referralForm.setConfirmTime(entity.getConfirmTime());
        referralForm.setCreateTime(entity.getCreateTime());
        
        return referralForm;
    }
    
    /**
     * 持久化实体列表转换为领域模型列表
     */
    public List<ReferralForm> toDomainList(List<ReferralFormEntity> entities) {
        if (entities == null) {
            return null;
        }
        
        return entities.stream()
                .map(this::toDomain)
                .collect(Collectors.toList());
    }
}
