package com.hys.hm.infrastructure.persistence.referral.repository;

import com.hys.hm.infrastructure.persistence.referral.entity.ReferralFormEntity;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 转诊表单JPA仓储接口
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-23
 */
@Repository
public interface ReferralFormJpaRepository extends JpaRepository<ReferralFormEntity, String> {
    
    /**
     * 根据转诊编号查找
     */
    Optional<ReferralFormEntity> findByReferralNo(String referralNo);
    
    /**
     * 根据基础信息ID查找
     */
    List<ReferralFormEntity> findByBasicInfoIdOrderByReferralDateDesc(String basicInfoId);
    
    /**
     * 根据转出医院查找
     */
    List<ReferralFormEntity> findByOutUnitIdOrderByReferralDateDesc(String outUnitId);
    
    /**
     * 根据转入医院查找
     */
    List<ReferralFormEntity> findByInUnitIdOrderByReferralDateDesc(String inUnitId);
    
    /**
     * 根据转出医生查找
     */
    List<ReferralFormEntity> findByOutDoctorIdOrderByReferralDateDesc(String outDoctorId);
    
    /**
     * 根据转入医生查找
     */
    List<ReferralFormEntity> findByInDoctorIdOrderByReferralDateDesc(String inDoctorId);
    
    /**
     * 根据状态查找
     */
    List<ReferralFormEntity> findByStatusOrderByReferralDateDesc(Integer status);
    
    /**
     * 根据患者姓名模糊查找
     */
    List<ReferralFormEntity> findByPatientNameContainingOrderByReferralDateDesc(String patientName);
    
    /**
     * 根据时间范围查找
     */
    List<ReferralFormEntity> findByReferralDateBetweenOrderByReferralDateDesc(LocalDateTime startDate, LocalDateTime endDate);
    
    /**
     * 分页查找
     */
    List<ReferralFormEntity> findAllByOrderByReferralDateDesc(Pageable pageable);
    
    /**
     * 根据状态统计数量
     */
    long countByStatus(Integer status);
    
    /**
     * 根据转出医院统计数量
     */
    long countByOutUnitId(String outUnitId);
    
    /**
     * 根据转入医院统计数量
     */
    long countByInUnitId(String inUnitId);
    
    /**
     * 查找超时的转诊表单
     */
    @Query("SELECT r FROM ReferralFormEntity r WHERE r.status = 0 AND r.referralDate < :timeoutDate ORDER BY r.referralDate DESC")
    List<ReferralFormEntity> findTimeoutReferrals(@Param("timeoutDate") LocalDateTime timeoutDate);
    
    /**
     * 根据条件查找（复杂查询）
     */
    @Query("SELECT r FROM ReferralFormEntity r WHERE " +
           "(:outUnitId IS NULL OR r.outUnitId = :outUnitId) AND " +
           "(:inUnitId IS NULL OR r.inUnitId = :inUnitId) AND " +
           "(:status IS NULL OR r.status = :status) AND " +
           "(:startDate IS NULL OR r.referralDate >= :startDate) AND " +
           "(:endDate IS NULL OR r.referralDate <= :endDate) " +
           "ORDER BY r.referralDate DESC")
    List<ReferralFormEntity> findByConditions(@Param("outUnitId") String outUnitId,
                                             @Param("inUnitId") String inUnitId,
                                             @Param("status") Integer status,
                                             @Param("startDate") LocalDateTime startDate,
                                             @Param("endDate") LocalDateTime endDate,
                                             Pageable pageable);
    
    /**
     * 批量更新状态
     */
    @Modifying
    @Query("UPDATE ReferralFormEntity r SET r.status = :status, r.updateTime = :updateTime WHERE r.id IN :ids")
    void updateStatusByIds(@Param("ids") List<String> ids, 
                          @Param("status") Integer status, 
                          @Param("updateTime") LocalDateTime updateTime);
    
    /**
     * 删除指定时间之前的记录
     */
    @Modifying
    @Query("DELETE FROM ReferralFormEntity r WHERE r.createTime < :dateTime")
    void deleteByCreateTimeBefore(@Param("dateTime") LocalDateTime dateTime);
    
    /**
     * 检查转诊编号是否存在
     */
    boolean existsByReferralNo(String referralNo);
}
