package com.hys.hm.infrastructure.persistence.referral.repository;

import com.hys.hm.domain.referral.model.ReferralForm;
import com.hys.hm.domain.referral.repository.ReferralFormRepository;
import com.hys.hm.infrastructure.persistence.service.EncryptFieldService;
import com.hys.hm.domain.referral.enums.ReferralStatus;
import com.hys.hm.infrastructure.persistence.referral.entity.ReferralFormEntity;
import com.hys.hm.infrastructure.persistence.referral.mapper.ReferralFormMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 转诊表单仓储实现
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-23
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class ReferralFormRepositoryImpl implements ReferralFormRepository {
    
    private final ReferralFormJpaRepository jpaRepository;
    private final ReferralFormMapper referralFormMapper;
    private final EncryptFieldService encryptFieldService;
    
    @Override
    public ReferralForm save(ReferralForm referralForm) {
        ReferralFormEntity entity = referralFormMapper.toEntity(referralForm);
        ReferralFormEntity savedEntity = jpaRepository.save(entity);
        return referralFormMapper.toDomain(savedEntity);
    }
    
    @Override
    public Optional<ReferralForm> findById(String id) {
        return jpaRepository.findById(id)
                .map(referralFormMapper::toDomain);
    }
    
    @Override
    public Optional<ReferralForm> findByReferralNo(String referralNo) {
        return jpaRepository.findByReferralNo(referralNo)
                .map(referralFormMapper::toDomain);
    }
    
    @Override
    public List<ReferralForm> findByBasicInfoId(String basicInfoId) {
        List<ReferralFormEntity> entities = jpaRepository.findByBasicInfoIdOrderByReferralDateDesc(basicInfoId);
        return referralFormMapper.toDomainList(entities);
    }
    
    @Override
    public List<ReferralForm> findByOutHospital(String outUnitId) {
        List<ReferralFormEntity> entities = jpaRepository.findByOutUnitIdOrderByReferralDateDesc(outUnitId);
        return referralFormMapper.toDomainList(entities);
    }
    
    @Override
    public List<ReferralForm> findByInHospital(String inUnitId) {
        List<ReferralFormEntity> entities = jpaRepository.findByInUnitIdOrderByReferralDateDesc(inUnitId);
        return referralFormMapper.toDomainList(entities);
    }
    
    @Override
    public List<ReferralForm> findByOutDoctor(String outDoctorId) {
        List<ReferralFormEntity> entities = jpaRepository.findByOutDoctorIdOrderByReferralDateDesc(outDoctorId);
        return referralFormMapper.toDomainList(entities);
    }
    
    @Override
    public List<ReferralForm> findByInDoctor(String inDoctorId) {
        List<ReferralFormEntity> entities = jpaRepository.findByInDoctorIdOrderByReferralDateDesc(inDoctorId);
        return referralFormMapper.toDomainList(entities);
    }
    
    @Override
    public List<ReferralForm> findByStatus(ReferralStatus status) {
        List<ReferralFormEntity> entities = jpaRepository.findByStatusOrderByReferralDateDesc(status.getCode());
        return referralFormMapper.toDomainList(entities);
    }
    
    @Override
    public List<ReferralForm> findByPatientNameLike(String patientName) {
        List<ReferralFormEntity> entities = jpaRepository.findByPatientNameContainingOrderByReferralDateDesc(patientName);
        return referralFormMapper.toDomainList(entities);
    }
    
    @Override
    public List<ReferralForm> findByReferralDateBetween(LocalDateTime startDate, LocalDateTime endDate) {
        List<ReferralFormEntity> entities = jpaRepository.findByReferralDateBetweenOrderByReferralDateDesc(startDate, endDate);
        return referralFormMapper.toDomainList(entities);
    }
    
    @Override
    public List<ReferralForm> findWithPagination(int page, int size) {
        Pageable pageable = PageRequest.of(page - 1, size);
        List<ReferralFormEntity> entities = jpaRepository.findAllByOrderByReferralDateDesc(pageable);
        return referralFormMapper.toDomainList(entities);
    }
    
    @Override
    public List<ReferralForm> findByConditionsWithPagination(String outUnitId, String inUnitId, 
                                                            ReferralStatus status, LocalDateTime startDate, 
                                                            LocalDateTime endDate, int page, int size) {
        Pageable pageable = PageRequest.of(page - 1, size);
        Integer statusCode = status != null ? status.getCode() : null;
        
        List<ReferralFormEntity> entities = jpaRepository.findByConditions(
            outUnitId, inUnitId, statusCode, startDate, endDate, pageable
        );
        
        return referralFormMapper.toDomainList(entities);
    }
    
    @Override
    public long count() {
        return jpaRepository.count();
    }
    
    @Override
    public long countByStatus(ReferralStatus status) {
        return jpaRepository.countByStatus(status.getCode());
    }
    
    @Override
    public long countByOutHospital(String outUnitId) {
        return jpaRepository.countByOutUnitId(outUnitId);
    }
    
    @Override
    public long countByInHospital(String inUnitId) {
        return jpaRepository.countByInUnitId(inUnitId);
    }
    
    @Override
    public List<ReferralForm> findTimeoutReferrals(int timeoutDays) {
        LocalDateTime timeoutDate = LocalDateTime.now().minusDays(timeoutDays);
        List<ReferralFormEntity> entities = jpaRepository.findTimeoutReferrals(timeoutDate);
        return referralFormMapper.toDomainList(entities);
    }
    
    @Override
    public void deleteById(String id) {
        jpaRepository.deleteById(id);
    }
    
    @Override
    public boolean existsByReferralNo(String referralNo) {
        return jpaRepository.existsByReferralNo(referralNo);
    }



    @Override
    public List<ReferralForm> findByIdCard(String idCard, boolean exactMatch) {
        log.info("根据身份证号查询转诊表单，身份证号: {}, 精确匹配: {}", maskSensitiveData(idCard), exactMatch);

        try {
            List<String> entityIds;
            if (exactMatch) {
                entityIds = encryptFieldService.findByExactMatch("ReferralFormEntity", "idCard", idCard);
            } else {
                entityIds = encryptFieldService.findByFuzzyMatch("ReferralFormEntity", "idCard", idCard, 4);
            }

            if (entityIds.isEmpty()) {
                log.debug("根据身份证号未找到匹配的转诊表单");
                return List.of();
            }

            log.debug("根据身份证号找到 {} 条匹配记录", entityIds.size());
            List<ReferralFormEntity> entities = jpaRepository.findAllById(entityIds);
            return referralFormMapper.toDomainList(entities);

        } catch (Exception e) {
            log.error("根据身份证号查询转诊表单失败: {}", e.getMessage(), e);
            return List.of();
        }
    }

    @Override
    public List<ReferralForm> findByPhone(String phone, boolean exactMatch) {
        log.info("根据手机号查询转诊表单，手机号: {}, 精确匹配: {}", maskSensitiveData(phone), exactMatch);

        try {
            List<String> entityIds;
            if (exactMatch) {
                entityIds = encryptFieldService.findByExactMatch("ReferralFormEntity", "phone", phone);
            } else {
                entityIds = encryptFieldService.findByFuzzyMatch("ReferralFormEntity", "phone", phone, 3);
            }

            if (entityIds.isEmpty()) {
                log.debug("根据手机号未找到匹配的转诊表单");
                return List.of();
            }

            log.debug("根据手机号找到 {} 条匹配记录", entityIds.size());
            List<ReferralFormEntity> entities = jpaRepository.findAllById(entityIds);
            return referralFormMapper.toDomainList(entities);

        } catch (Exception e) {
            log.error("根据手机号查询转诊表单失败: {}", e.getMessage(), e);
            return List.of();
        }
    }

    /**
     * 脱敏敏感数据用于日志显示
     */
    private String maskSensitiveData(String data) {
        if (data == null || data.length() <= 4) {
            return "****";
        }
        return data.substring(0, 3) + "****" + data.substring(data.length() - 1);
    }

}
