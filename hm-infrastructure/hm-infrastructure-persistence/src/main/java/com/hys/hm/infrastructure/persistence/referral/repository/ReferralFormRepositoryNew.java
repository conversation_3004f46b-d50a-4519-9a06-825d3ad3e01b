package com.hys.hm.infrastructure.persistence.referral.repository;

import com.hys.hm.infrastructure.persistence.referral.entity.ReferralFormEntityNew;
import com.hys.hm.shared.framework.repository.BaseRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 转诊表单仓储接口（使用新框架）
 * 继承BaseRepository获得完整的CRUD和动态查询功能
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-29
 */
@Repository
public interface ReferralFormRepositoryNew extends BaseRepository<ReferralFormEntityNew, String> {

    /**
     * 根据转诊编号查找转诊表单
     * 
     * @param referralNo 转诊编号
     * @return 转诊表单（可能为空）
     */
    Optional<ReferralFormEntityNew> findByReferralNo(String referralNo);

    /**
     * 根据基础信息ID查找转诊表单列表
     * 
     * @param basicInfoId 基础信息ID
     * @return 转诊表单列表
     */
    List<ReferralFormEntityNew> findByBasicInfoId(String basicInfoId);

    /**
     * 根据转出医院查找转诊表单列表
     * 
     * @param outUnitId 转出医院ID
     * @return 转诊表单列表
     */
    List<ReferralFormEntityNew> findByOutUnitId(String outUnitId);

    /**
     * 根据转入医院查找转诊表单列表
     * 
     * @param inUnitId 转入医院ID
     * @return 转诊表单列表
     */
    List<ReferralFormEntityNew> findByInUnitId(String inUnitId);

    /**
     * 根据转出医生查找转诊表单列表
     * 
     * @param outDoctorId 转出医生ID
     * @return 转诊表单列表
     */
    List<ReferralFormEntityNew> findByOutDoctorId(String outDoctorId);

    /**
     * 根据转入医生查找转诊表单列表
     * 
     * @param inDoctorId 转入医生ID
     * @return 转诊表单列表
     */
    List<ReferralFormEntityNew> findByInDoctorId(String inDoctorId);

    /**
     * 根据状态查找转诊表单列表
     * 
     * @param status 转诊状态
     * @return 转诊表单列表
     */
    List<ReferralFormEntityNew> findByStatus(Integer status);

    /**
     * 根据患者姓名模糊查找转诊表单列表
     * 
     * @param patientName 患者姓名
     * @return 转诊表单列表
     */
    List<ReferralFormEntityNew> findByPatientNameContaining(String patientName);

    /**
     * 根据时间范围查找转诊表单列表
     * 
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 转诊表单列表
     */
    List<ReferralFormEntityNew> findByReferralDateBetween(LocalDateTime startDate, LocalDateTime endDate);

    /**
     * 根据状态统计转诊表单数量
     * 
     * @param status 转诊状态
     * @return 数量
     */
    long countByStatus(Integer status);

    /**
     * 根据转出医院统计转诊表单数量
     * 
     * @param outUnitId 转出医院ID
     * @return 数量
     */
    long countByOutUnitId(String outUnitId);

    /**
     * 根据转入医院统计转诊表单数量
     * 
     * @param inUnitId 转入医院ID
     * @return 数量
     */
    long countByInUnitId(String inUnitId);

    /**
     * 检查转诊编号是否存在
     * 
     * @param referralNo 转诊编号
     * @return 是否存在
     */
    boolean existsByReferralNo(String referralNo);

    /**
     * 查找超时的转诊表单（超过指定天数未确认）
     * 
     * @param timeoutDays 超时天数
     * @return 超时的转诊表单列表
     */
    @Query("SELECT r FROM ReferralFormEntityNew r WHERE r.status = 1 AND r.referralDate < :cutoffTime AND r.deleted = 0")
    List<ReferralFormEntityNew> findTimeoutReferrals(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 查找待处理的转诊表单
     * 
     * @return 待处理的转诊表单列表
     */
    @Query("SELECT r FROM ReferralFormEntityNew r WHERE r.status = 1 AND r.deleted = 0 ORDER BY r.referralDate ASC")
    List<ReferralFormEntityNew> findPendingReferrals();

    /**
     * 查找紧急转诊表单
     * 
     * @return 紧急转诊表单列表
     */
    @Query("SELECT r FROM ReferralFormEntityNew r WHERE r.urgencyLevel >= 2 AND r.deleted = 0 ORDER BY r.urgencyLevel DESC, r.referralDate ASC")
    List<ReferralFormEntityNew> findUrgentReferrals();

    /**
     * 根据医院和状态统计转诊表单数量
     * 
     * @param unitId 医院ID（转出或转入）
     * @param status 状态
     * @return 数量
     */
    @Query("SELECT COUNT(r) FROM ReferralFormEntityNew r WHERE (r.outUnitId = :unitId OR r.inUnitId = :unitId) AND r.status = :status AND r.deleted = 0")
    long countByUnitIdAndStatus(@Param("unitId") String unitId, @Param("status") Integer status);

    /**
     * 根据医生查找转诊表单列表
     * 
     * @param doctorId 医生ID（转出或转入）
     * @return 转诊表单列表
     */
    @Query("SELECT r FROM ReferralFormEntityNew r WHERE (r.outDoctorId = :doctorId OR r.inDoctorId = :doctorId) AND r.deleted = 0 ORDER BY r.referralDate DESC")
    List<ReferralFormEntityNew> findByDoctorId(@Param("doctorId") String doctorId);

    /**
     * 根据时间范围和状态查找转诊表单列表
     * 
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param status 状态
     * @return 转诊表单列表
     */
    @Query("SELECT r FROM ReferralFormEntityNew r WHERE r.referralDate BETWEEN :startDate AND :endDate AND r.status = :status AND r.deleted = 0 ORDER BY r.referralDate DESC")
    List<ReferralFormEntityNew> findByDateRangeAndStatus(@Param("startDate") LocalDateTime startDate, 
                                                         @Param("endDate") LocalDateTime endDate, 
                                                         @Param("status") Integer status);

    /**
     * 根据紧急程度查找转诊表单列表
     * 
     * @param urgencyLevel 紧急程度
     * @return 转诊表单列表
     */
    List<ReferralFormEntityNew> findByUrgencyLevel(Integer urgencyLevel);

    /**
     * 查找今日转诊表单
     * 
     * @param startOfDay 今日开始时间
     * @param endOfDay 今日结束时间
     * @return 今日转诊表单列表
     */
    @Query("SELECT r FROM ReferralFormEntityNew r WHERE r.referralDate BETWEEN :startOfDay AND :endOfDay AND r.deleted = 0 ORDER BY r.referralDate DESC")
    List<ReferralFormEntityNew> findTodayReferrals(@Param("startOfDay") LocalDateTime startOfDay, 
                                                   @Param("endOfDay") LocalDateTime endOfDay);

    /**
     * 统计今日转诊表单数量
     * 
     * @param startOfDay 今日开始时间
     * @param endOfDay 今日结束时间
     * @return 今日转诊表单数量
     */
    @Query("SELECT COUNT(r) FROM ReferralFormEntityNew r WHERE r.referralDate BETWEEN :startOfDay AND :endOfDay AND r.deleted = 0")
    long countTodayReferrals(@Param("startOfDay") LocalDateTime startOfDay, 
                            @Param("endOfDay") LocalDateTime endOfDay);

    /**
     * 根据转诊编号前缀查找最大编号
     * 
     * @param prefix 编号前缀
     * @return 最大编号
     */
    @Query("SELECT MAX(r.referralNo) FROM ReferralFormEntityNew r WHERE r.referralNo LIKE :prefix AND r.deleted = 0")
    String findMaxReferralNoByPrefix(@Param("prefix") String prefix);

    /**
     * 更新转诊状态
     * 
     * @param id 转诊表单ID
     * @param status 新状态
     * @param confirmTime 确认时间
     * @return 更新的记录数
     */
    @Query("UPDATE ReferralFormEntityNew r SET r.status = :status, r.confirmTime = :confirmTime WHERE r.id = :id")
    int updateStatus(@Param("id") String id, @Param("status") Integer status, @Param("confirmTime") LocalDateTime confirmTime);

    /**
     * 更新拒绝信息
     * 
     * @param id 转诊表单ID
     * @param status 新状态
     * @param rejectReason 拒绝原因
     * @param confirmTime 确认时间
     * @return 更新的记录数
     */
    @Query("UPDATE ReferralFormEntityNew r SET r.status = :status, r.rejectReason = :rejectReason, r.confirmTime = :confirmTime WHERE r.id = :id")
    int updateRejectInfo(@Param("id") String id, @Param("status") Integer status, 
                        @Param("rejectReason") String rejectReason, @Param("confirmTime") LocalDateTime confirmTime);

    /**
     * 根据身份证号查询转诊表单（支持加密字段模糊查询）
     * 
     * @param idCardToken 身份证号令牌
     * @return 转诊表单列表
     */
    @Query("SELECT r FROM ReferralFormEntityNew r WHERE r.idCard LIKE %:idCardToken% AND r.deleted = 0")
    List<ReferralFormEntityNew> findByIdCardToken(@Param("idCardToken") String idCardToken);

    /**
     * 根据手机号查询转诊表单（支持加密字段模糊查询）
     * 
     * @param phoneToken 手机号令牌
     * @return 转诊表单列表
     */
    @Query("SELECT r FROM ReferralFormEntityNew r WHERE r.phone LIKE %:phoneToken% AND r.deleted = 0")
    List<ReferralFormEntityNew> findByPhoneToken(@Param("phoneToken") String phoneToken);
}
