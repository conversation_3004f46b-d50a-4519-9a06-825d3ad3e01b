package com.hys.hm.infrastructure.persistence.repository;

import com.hys.hm.infrastructure.persistence.entity.EncryptSearchEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 加密字段搜索索引仓储
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-23
 */
@Repository
public interface EncryptSearchRepository extends JpaRepository<EncryptSearchEntity, Long> {

    /**
     * 根据实体和字段删除索引
     */
    @Modifying
    @Query("DELETE FROM EncryptSearchEntity e WHERE e.entityType = :entityType AND e.entityId = :entityId AND e.fieldName = :fieldName")
    void deleteByEntityAndField(@Param("entityType") String entityType,
                               @Param("entityId") String entityId,
                               @Param("fieldName") String fieldName);

    /**
     * 根据实体删除所有索引
     */
    @Modifying
    @Query("DELETE FROM EncryptSearchEntity e WHERE e.entityType = :entityType AND e.entityId = :entityId")
    void deleteByEntity(@Param("entityType") String entityType, @Param("entityId") String entityId);

    /**
     * 根据分词哈希查找实体ID
     */
    @Query("SELECT DISTINCT e.entityId FROM EncryptSearchEntity e WHERE e.entityType = :entityType AND e.fieldName = :fieldName AND e.tokenHash = :tokenHash")
    List<String> findEntityIdsByTokenHash(@Param("entityType") String entityType,
                                         @Param("fieldName") String fieldName,
                                         @Param("tokenHash") String tokenHash);

    /**
     * 根据精确哈希查找实体ID
     */
    @Query("SELECT DISTINCT e.entityId FROM EncryptSearchEntity e WHERE e.entityType = :entityType AND e.fieldName = :fieldName AND e.exactHash = :exactHash")
    List<String> findEntityIdsByExactHash(@Param("entityType") String entityType,
                                         @Param("fieldName") String fieldName,
                                         @Param("exactHash") String exactHash);

    /**
     * 根据多个分词哈希查找实体ID（模糊查询）
     */
    @Query("SELECT e.entityId, COUNT(e.entityId) as matchCount FROM EncryptSearchEntity e " +
           "WHERE e.entityType = :entityType AND e.fieldName = :fieldName AND e.tokenHash IN :tokenHashes " +
           "GROUP BY e.entityId ORDER BY matchCount DESC")
    List<Object[]> findEntityIdsByTokenHashes(@Param("entityType") String entityType,
                                             @Param("fieldName") String fieldName,
                                             @Param("tokenHashes") List<String> tokenHashes);

    /**
     * 删除指定时间之前的索引记录
     */
    @Modifying
    @Query("DELETE FROM EncryptSearchEntity e WHERE e.createTime < :cutoffTime")
    long deleteByCreateTimeBefore(@Param("cutoffTime") java.time.LocalDateTime cutoffTime);
}
