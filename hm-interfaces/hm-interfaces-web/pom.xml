<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.hys</groupId>
        <artifactId>hm-interfaces</artifactId>
        <version>3.0.1-SNAPSHOT</version>
    </parent>

    <artifactId>hm-interfaces-web</artifactId>
    <name>hm-interfaces-web</name>
    <description>Web控制器模块</description>

    <dependencies>
        <!-- 内部模块依赖 -->
        <dependency>
            <groupId>com.hys</groupId>
            <artifactId>hm-shared-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hys</groupId>
            <artifactId>hm-shared-types</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hys</groupId>
            <artifactId>hm-shared-logging</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hys</groupId>
            <artifactId>hm-application-patient</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hys</groupId>
            <artifactId>hm-application-health</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hys</groupId>
            <artifactId>hm-application-referral</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hys</groupId>
            <artifactId>hm-shared-framework</artifactId>
        </dependency>

        <!-- Spring Boot Web -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <!-- Swagger/OpenAPI 文档 -->
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
            <version>2.2.0</version>
        </dependency>

        <!-- ReDoc UI 通过 CDN 引入，无需额外依赖 -->

        <!-- 参数验证 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <!-- Lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
    </dependencies>

</project>
