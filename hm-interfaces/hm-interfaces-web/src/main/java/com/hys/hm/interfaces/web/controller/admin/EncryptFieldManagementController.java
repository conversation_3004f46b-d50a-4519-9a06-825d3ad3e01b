package com.hys.hm.interfaces.web.controller.admin;

import com.hys.hm.shared.common.service.EncryptService;
import com.hys.hm.infrastructure.persistence.service.EncryptFieldService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 加密字段管理控制器
 * 提供加密字段的管理和监控功能
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-23
 */
@Slf4j
@RestController
@RequestMapping("/api/admin/encrypt")
@RequiredArgsConstructor
@Tag(name = "加密字段管理", description = "加密字段管理和监控接口")
public class EncryptFieldManagementController {
    
    private final EncryptService encryptService;
    private final EncryptFieldService encryptFieldService;
    private final JdbcTemplate jdbcTemplate;
    
    /**
     * 获取加密字段统计信息
     */
    @GetMapping("/statistics")
    @Operation(summary = "获取加密字段统计", description = "获取加密字段的统计信息")
    public Map<String, Object> getEncryptStatistics() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 统计索引总数
            Integer totalIndexes = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM encrypt_search_index", Integer.class);
            
            // 按实体类型统计
            List<Map<String, Object>> entityStats = jdbcTemplate.queryForList("""
                SELECT entity_type, COUNT(*) as count 
                FROM encrypt_search_index 
                GROUP BY entity_type
                """);
            
            // 按字段名统计
            List<Map<String, Object>> fieldStats = jdbcTemplate.queryForList("""
                SELECT entity_type, field_name, COUNT(*) as count 
                FROM encrypt_search_index 
                GROUP BY entity_type, field_name
                """);
            
            // 按索引类型统计
            Integer exactIndexes = jdbcTemplate.queryForObject("""
                SELECT COUNT(*) FROM encrypt_search_index 
                WHERE exact_hash IS NOT NULL
                """, Integer.class);
            
            Integer fuzzyIndexes = jdbcTemplate.queryForObject("""
                SELECT COUNT(*) FROM encrypt_search_index 
                WHERE token_hash IS NOT NULL
                """, Integer.class);
            
            result.put("totalIndexes", totalIndexes);
            result.put("exactIndexes", exactIndexes);
            result.put("fuzzyIndexes", fuzzyIndexes);
            result.put("entityStatistics", entityStats);
            result.put("fieldStatistics", fieldStats);
            result.put("success", true);
            
        } catch (Exception e) {
            log.error("获取加密字段统计失败: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 搜索加密字段索引
     */
    @PostMapping("/search-indexes")
    @Operation(summary = "搜索加密字段索引", description = "根据条件搜索加密字段索引")
    public Map<String, Object> searchIndexes(
            @Parameter(description = "实体类型") @RequestParam(required = false) String entityType,
            @Parameter(description = "字段名") @RequestParam(required = false) String fieldName,
            @Parameter(description = "页码", example = "1") @RequestParam(defaultValue = "1") int page,
            @Parameter(description = "页大小", example = "20") @RequestParam(defaultValue = "20") int size) {
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            StringBuilder sql = new StringBuilder("""
                SELECT entity_type, entity_id, field_name, 
                       CASE WHEN exact_hash IS NOT NULL THEN 'EXACT' ELSE 'FUZZY' END as index_type,
                       create_time, update_time
                FROM encrypt_search_index 
                WHERE 1=1
                """);
            
            if (entityType != null && !entityType.trim().isEmpty()) {
                sql.append(" AND entity_type = '").append(entityType).append("'");
            }
            
            if (fieldName != null && !fieldName.trim().isEmpty()) {
                sql.append(" AND field_name = '").append(fieldName).append("'");
            }
            
            sql.append(" ORDER BY create_time DESC");
            sql.append(" LIMIT ").append(size).append(" OFFSET ").append((page - 1) * size);
            
            List<Map<String, Object>> indexes = jdbcTemplate.queryForList(sql.toString());
            
            // 统计总数
            StringBuilder countSql = new StringBuilder("SELECT COUNT(*) FROM encrypt_search_index WHERE 1=1");
            if (entityType != null && !entityType.trim().isEmpty()) {
                countSql.append(" AND entity_type = '").append(entityType).append("'");
            }
            if (fieldName != null && !fieldName.trim().isEmpty()) {
                countSql.append(" AND field_name = '").append(fieldName).append("'");
            }
            
            Integer total = jdbcTemplate.queryForObject(countSql.toString(), Integer.class);
            
            result.put("indexes", indexes);
            result.put("total", total);
            result.put("page", page);
            result.put("size", size);
            result.put("totalPages", (total + size - 1) / size);
            result.put("success", true);
            
        } catch (Exception e) {
            log.error("搜索加密字段索引失败: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 重建指定实体的加密索引
     */
    @PostMapping("/rebuild-index")
    @Operation(summary = "重建加密索引", description = "重建指定实体的加密索引")
    public Map<String, Object> rebuildIndex(
            @Parameter(description = "实体类型", required = true) @RequestParam String entityType,
            @Parameter(description = "实体ID", required = true) @RequestParam String entityId) {
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 删除旧索引
            encryptFieldService.deleteSearchIndex(entityType, entityId);
            
            // 这里需要根据实体类型和ID重新创建索引
            // 由于我们没有通用的实体加载机制，这里只是示例
            log.info("重建加密索引: entityType={}, entityId={}", entityType, entityId);
            
            result.put("entityType", entityType);
            result.put("entityId", entityId);
            result.put("message", "索引重建请求已提交");
            result.put("success", true);
            
        } catch (Exception e) {
            log.error("重建加密索引失败: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 清理过期的加密索引
     */
    @PostMapping("/cleanup-expired")
    @Operation(summary = "清理过期索引", description = "清理指定天数之前的过期索引")
    public Map<String, Object> cleanupExpiredIndexes(
            @Parameter(description = "保留天数", example = "90") @RequestParam(defaultValue = "90") int retentionDays) {
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            String sql = """
                DELETE FROM encrypt_search_index 
                WHERE create_time < DATE_SUB(NOW(), INTERVAL ? DAY)
                """;
            
            int deletedCount = jdbcTemplate.update(sql, retentionDays);
            
            result.put("deletedCount", deletedCount);
            result.put("retentionDays", retentionDays);
            result.put("success", true);
            
            log.info("清理过期加密索引完成，删除记录数: {}, 保留天数: {}", deletedCount, retentionDays);
            
        } catch (Exception e) {
            log.error("清理过期加密索引失败: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 验证加密字段查询性能
     */
    @PostMapping("/performance-test")
    @Operation(summary = "性能测试", description = "测试加密字段查询性能")
    public Map<String, Object> performanceTest(
            @Parameter(description = "测试查询文本", required = true) @RequestParam String searchText,
            @Parameter(description = "测试次数", example = "100") @RequestParam(defaultValue = "100") int iterations) {
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            long startTime = System.currentTimeMillis();
            
            for (int i = 0; i < iterations; i++) {
                // 测试精确查询
                List<String> exactResults = encryptFieldService.findByExactMatch(
                    "ReferralFormEntity", "phone", searchText);
                
                // 测试模糊查询
                List<String> fuzzyResults = encryptFieldService.findByFuzzyMatch(
                    "ReferralFormEntity", "phone", searchText, 3);
            }
            
            long endTime = System.currentTimeMillis();
            long totalTime = endTime - startTime;
            double avgTime = (double) totalTime / iterations;
            
            result.put("iterations", iterations);
            result.put("totalTimeMs", totalTime);
            result.put("averageTimeMs", avgTime);
            result.put("qps", 1000.0 / avgTime);
            result.put("success", true);
            
            log.info("加密字段查询性能测试完成: {} 次查询，总耗时 {} ms，平均耗时 {} ms", 
                    iterations, totalTime, avgTime);
            
        } catch (Exception e) {
            log.error("性能测试失败: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return result;
    }
}
