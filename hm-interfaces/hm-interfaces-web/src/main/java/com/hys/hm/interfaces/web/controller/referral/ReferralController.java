package com.hys.hm.interfaces.web.controller.referral;

import com.hys.hm.application.referral.dto.ReferralCreateDTO;
import com.hys.hm.application.referral.dto.ReferralQueryDTO;
import com.hys.hm.application.referral.service.ReferralApplicationService;
import com.hys.hm.shared.common.Result;
import com.hys.hm.shared.framework.controller.BaseController;
import com.hys.hm.shared.types.dto.ReferralSummaryDTO;
import com.hys.hm.shared.types.enums.ReferralStatus;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 转诊表单控制器
 * 继承BaseController获得完整的REST API功能
 * 提供转诊相关的Web接口
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-29
 */
@RestController
@RequestMapping("/api/referrals")
@Tag(name = "转诊管理", description = "转诊表单的CRUD操作和业务功能")
@Slf4j
@RequiredArgsConstructor
public class ReferralController extends BaseController<ReferralQueryDTO, String> {

    private final ReferralApplicationService referralApplicationService;

    @Override
    protected String getEntityId(ReferralQueryDTO entity) {
        return entity.getId();
    }

    @Override
    protected void setEntityId(ReferralQueryDTO entity, String id) {
        entity.setId(id);
    }

    // ========== 业务操作接口 ==========

    /**
     * 创建转诊表单
     */
    @PostMapping
    @Operation(summary = "创建转诊表单", description = "创建新的转诊表单")
    public Result<ReferralQueryDTO> createReferral(
            @Valid @RequestBody ReferralCreateDTO createDTO,
            @Parameter(description = "当前用户ID", required = true) @RequestHeader("X-User-Id") String currentUserId) {
        try {
            log.info("创建转诊表单: patientName={}, operatorId={}",
                    createDTO.getPatientName(), currentUserId);

            ReferralQueryDTO result = referralApplicationService.createReferralForm(createDTO, currentUserId);

            return Result.success("创建成功", result);

        } catch (IllegalArgumentException e) {
            log.warn("创建转诊表单参数错误: {}", e.getMessage());
            return Result.error(Result.PARAM_ERROR_CODE, e.getMessage());
        } catch (Exception e) {
            log.error("创建转诊表单异常: {}", e.getMessage(), e);
            return Result.error("创建失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID获取转诊表单详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取转诊表单详情", description = "根据ID获取转诊表单的详细信息")
    public Result<ReferralQueryDTO> getReferralDetail(
            @Parameter(description = "转诊表单ID", required = true) @PathVariable @NotNull String id) {
        try {
            log.debug("获取转诊表单详情: id={}", id);

            Optional<ReferralQueryDTO> result = referralApplicationService.getReferralDetail(id);

            return result.map(referralQueryDTO ->
                    Result.success("查询成功", referralQueryDTO)).orElseGet(() ->
                    Result.error(Result.NOT_FOUND_CODE, "转诊表单不存在: ID=" + id));

        } catch (Exception e) {
            log.error("获取转诊表单详情异常: id={}, error={}", id, e.getMessage(), e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据转诊编号获取转诊表单详情
     */
    @GetMapping("/by-no/{referralNo}")
    @Operation(summary = "根据转诊编号获取转诊表单详情", description = "根据转诊编号获取转诊表单的详细信息")
    public ResponseEntity<Result<ReferralQueryDTO>> getReferralDetailByNo(
            @Parameter(description = "转诊编号", required = true) @PathVariable @NotNull String referralNo) {
        try {
            log.debug("根据转诊编号获取转诊表单详情: referralNo={}", referralNo);

            Optional<ReferralQueryDTO> result = referralApplicationService.getReferralDetailByNo(referralNo);

            return result.map(referralQueryDTO ->
                    ResponseEntity.ok(Result.success("查询成功", referralQueryDTO))).orElseGet(() ->
                    ResponseEntity.notFound().build());

        } catch (Exception e) {
            log.error("根据转诊编号获取转诊表单详情异常: referralNo={}, error={}", referralNo, e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Result.error("查询失败: " + e.getMessage()));
        }
    }

    /**
     * 确认转诊
     */
    @PutMapping("/{id}/confirm")
    @Operation(summary = "确认转诊", description = "确认转诊申请")
    public ResponseEntity<Result<ReferralQueryDTO>> confirmReferral(
            @Parameter(description = "转诊表单ID", required = true) @PathVariable @NotNull String id,
            @Parameter(description = "当前用户ID", required = true) @RequestHeader("X-User-Id") String currentUserId) {
        try {
            log.info("确认转诊: id={}, operatorId={}", id, currentUserId);

            ReferralQueryDTO result = referralApplicationService.confirmReferral(id, currentUserId);

            return ResponseEntity.ok(Result.success("确认成功", result));

        } catch (IllegalArgumentException e) {
            log.warn("确认转诊参数错误: id={}, error={}", id, e.getMessage());
            return ResponseEntity.badRequest().body(Result.error(Result.PARAM_ERROR_CODE, e.getMessage()));
        } catch (IllegalStateException e) {
            log.warn("确认转诊状态错误: id={}, error={}", id, e.getMessage());
            return ResponseEntity.badRequest().body(Result.error(Result.PARAM_ERROR_CODE, e.getMessage()));
        } catch (Exception e) {
            log.error("确认转诊异常: id={}, error={}", id, e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Result.error("确认失败: " + e.getMessage()));
        }
    }

    /**
     * 拒绝转诊
     */
    @PutMapping("/{id}/reject")
    @Operation(summary = "拒绝转诊", description = "拒绝转诊申请")
    public ResponseEntity<Result<ReferralQueryDTO>> rejectReferral(
            @Parameter(description = "转诊表单ID", required = true) @PathVariable @NotNull String id,
            @Parameter(description = "拒绝原因", required = true) @RequestParam @NotNull String reason,
            @Parameter(description = "当前用户ID", required = true) @RequestHeader("X-User-Id") String currentUserId) {
        try {
            log.info("拒绝转诊: id={}, reason={}, operatorId={}", id, reason, currentUserId);

            ReferralQueryDTO result = referralApplicationService.rejectReferral(id, reason, currentUserId);

            return ResponseEntity.ok(Result.success("拒绝成功", result));

        } catch (IllegalArgumentException e) {
            log.warn("拒绝转诊参数错误: id={}, error={}", id, e.getMessage());
            return ResponseEntity.badRequest().body(Result.error(Result.PARAM_ERROR_CODE, e.getMessage()));
        } catch (IllegalStateException e) {
            log.warn("拒绝转诊状态错误: id={}, error={}", id, e.getMessage());
            return ResponseEntity.badRequest().body(Result.error(Result.PARAM_ERROR_CODE, e.getMessage()));
        } catch (Exception e) {
            log.error("拒绝转诊异常: id={}, error={}", id, e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Result.error("拒绝失败: " + e.getMessage()));
        }
    }

    /**
     * 取消转诊
     */
    @PutMapping("/{id}/cancel")
    @Operation(summary = "取消转诊", description = "取消转诊申请")
    public ResponseEntity<Result<ReferralQueryDTO>> cancelReferral(
            @Parameter(description = "转诊表单ID", required = true) @PathVariable @NotNull String id,
            @Parameter(description = "当前用户ID", required = true) @RequestHeader("X-User-Id") String currentUserId) {
        try {
            log.info("取消转诊: id={}, operatorId={}", id, currentUserId);

            ReferralQueryDTO result = referralApplicationService.cancelReferral(id, currentUserId);

            return ResponseEntity.ok(Result.success("取消成功", result));

        } catch (IllegalArgumentException e) {
            log.warn("取消转诊参数错误: id={}, error={}", id, e.getMessage());
            return ResponseEntity.badRequest().body(Result.error(Result.PARAM_ERROR_CODE, e.getMessage()));
        } catch (IllegalStateException e) {
            log.warn("取消转诊状态错误: id={}, error={}", id, e.getMessage());
            return ResponseEntity.badRequest().body(Result.error(Result.PARAM_ERROR_CODE, e.getMessage()));
        } catch (Exception e) {
            log.error("取消转诊异常: id={}, error={}", id, e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Result.error("取消失败: " + e.getMessage()));
        }
    }

    /**
     * 完成转诊
     */
    @PutMapping("/{id}/complete")
    @Operation(summary = "完成转诊", description = "完成转诊流程")
    public ResponseEntity<Result<ReferralQueryDTO>> completeReferral(
            @Parameter(description = "转诊表单ID", required = true) @PathVariable @NotNull String id,
            @Parameter(description = "当前用户ID", required = true) @RequestHeader("X-User-Id") String currentUserId) {
        try {
            log.info("完成转诊: id={}, operatorId={}", id, currentUserId);

            ReferralQueryDTO result = referralApplicationService.completeReferral(id, currentUserId);

            return ResponseEntity.ok(Result.success("完成成功", result));

        } catch (IllegalArgumentException e) {
            log.warn("完成转诊参数错误: id={}, error={}", id, e.getMessage());
            return ResponseEntity.badRequest().body(Result.error(Result.PARAM_ERROR_CODE, e.getMessage()));
        } catch (IllegalStateException e) {
            log.warn("完成转诊状态错误: id={}, error={}", id, e.getMessage());
            return ResponseEntity.badRequest().body(Result.error(Result.PARAM_ERROR_CODE, e.getMessage()));
        } catch (Exception e) {
            log.error("完成转诊异常: id={}, error={}", id, e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Result.error("完成失败: " + e.getMessage()));
        }
    }

    /**
     * 批量确认转诊
     */
    @PutMapping("/batch-confirm")
    @Operation(summary = "批量确认转诊", description = "批量确认多个转诊申请")
    public ResponseEntity<Result<Integer>> batchConfirmReferrals(
            @Valid @RequestBody @NotEmpty List<String> ids,
            @Parameter(description = "当前用户ID", required = true) @RequestHeader("X-User-Id") String currentUserId) {
        try {
            log.info("批量确认转诊: 数量={}, operatorId={}", ids.size(), currentUserId);

            int successCount = referralApplicationService.batchConfirmReferrals(ids, currentUserId);

            return ResponseEntity.ok(Result.success("批量确认成功", successCount));

        } catch (Exception e) {
            log.error("批量确认转诊异常: ids={}, error={}", ids, e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Result.error("批量确认失败: " + e.getMessage()));
        }
    }

    // ========== 查询接口 ==========

    /**
     * 获取患者的转诊记录摘要
     */
    @GetMapping("/patient/{patientId}")
    @Operation(summary = "获取患者转诊记录", description = "获取指定患者的转诊记录摘要")
    public ResponseEntity<Result<List<ReferralSummaryDTO>>> getPatientReferrals(
            @Parameter(description = "患者ID", required = true) @PathVariable @NotNull String patientId) {
        try {
            log.debug("获取患者转诊记录: patientId={}", patientId);

            List<ReferralSummaryDTO> result = referralApplicationService.getPatientReferralSummary(patientId);

            return ResponseEntity.ok(Result.success("查询成功", result));

        } catch (Exception e) {
            log.error("获取患者转诊记录异常: patientId={}, error={}", patientId, e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Result.error("查询失败: " + e.getMessage()));
        }
    }

    /**
     * 获取待处理的转诊列表
     */
    @GetMapping("/pending")
    @Operation(summary = "获取待处理转诊", description = "获取所有待处理的转诊申请")
    public ResponseEntity<Result<List<ReferralSummaryDTO>>> getPendingReferrals() {
        try {
            log.debug("获取待处理转诊列表");

            List<ReferralSummaryDTO> result = referralApplicationService.getPendingReferrals();

            return ResponseEntity.ok(Result.success("查询成功", result));

        } catch (Exception e) {
            log.error("获取待处理转诊列表异常: error={}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Result.error("查询失败: " + e.getMessage()));
        }
    }

    /**
     * 获取紧急转诊列表
     */
    @GetMapping("/urgent")
    @Operation(summary = "获取紧急转诊", description = "获取所有紧急转诊申请")
    public ResponseEntity<Result<List<ReferralSummaryDTO>>> getUrgentReferrals() {
        try {
            log.debug("获取紧急转诊列表");

            List<ReferralSummaryDTO> result = referralApplicationService.getUrgentReferrals();

            return ResponseEntity.ok(Result.success("查询成功", result));

        } catch (Exception e) {
            log.error("获取紧急转诊列表异常: error={}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Result.error("查询失败: " + e.getMessage()));
        }
    }

    /**
     * 获取今日转诊列表
     */
    @GetMapping("/today")
    @Operation(summary = "获取今日转诊", description = "获取今天创建的转诊申请")
    public ResponseEntity<Result<List<ReferralSummaryDTO>>> getTodayReferrals() {
        try {
            log.debug("获取今日转诊列表");

            List<ReferralSummaryDTO> result = referralApplicationService.getTodayReferrals();

            return ResponseEntity.ok(Result.success("查询成功", result));

        } catch (Exception e) {
            log.error("获取今日转诊列表异常: error={}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Result.error("查询失败: " + e.getMessage()));
        }
    }

    /**
     * 根据状态获取转诊列表
     */
    @GetMapping("/status/{status}")
    @Operation(summary = "根据状态获取转诊", description = "根据状态获取转诊申请列表")
    public ResponseEntity<Result<List<ReferralSummaryDTO>>> getReferralsByStatus(
            @Parameter(description = "转诊状态", required = true) @PathVariable @NotNull ReferralStatus status) {
        try {
            log.debug("根据状态获取转诊列表: status={}", status);

            List<ReferralSummaryDTO> result = referralApplicationService.getReferralsByStatus(status);

            return ResponseEntity.ok(Result.success("查询成功", result));

        } catch (Exception e) {
            log.error("根据状态获取转诊列表异常: status={}, error={}", status, e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Result.error("查询失败: " + e.getMessage()));
        }
    }

    /**
     * 获取转诊统计信息
     */
    @GetMapping("/statistics")
    @Operation(summary = "获取转诊统计", description = "获取转诊相关的统计信息")
    public ResponseEntity<Result<Map<String, Object>>> getReferralStatistics(
            @Parameter(description = "医院ID（可选）") @RequestParam(required = false) String unitId,
            @Parameter(description = "开始时间（可选）") @RequestParam(required = false) LocalDateTime startDate,
            @Parameter(description = "结束时间（可选）") @RequestParam(required = false) LocalDateTime endDate) {
        try {
            log.debug("获取转诊统计信息: unitId={}, startDate={}, endDate={}", unitId, startDate, endDate);

            Map<String, Object> result = referralApplicationService.getReferralStatistics(unitId, startDate, endDate);

            return ResponseEntity.ok(Result.success("查询成功", result));

        } catch (Exception e) {
            log.error("获取转诊统计信息异常: unitId={}, startDate={}, endDate={}, error={}",
                    unitId, startDate, endDate, e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Result.error("查询失败: " + e.getMessage()));
        }
    }

    /**
     * 生成转诊编号
     */
    @GetMapping("/generate-no")
    @Operation(summary = "生成转诊编号", description = "生成新的转诊编号")
    public ResponseEntity<Result<String>> generateReferralNo() {
        try {
            log.debug("生成转诊编号");

            String referralNo = referralApplicationService.generateReferralNo();

            return ResponseEntity.ok(Result.success("生成成功", referralNo));

        } catch (Exception e) {
            log.error("生成转诊编号异常: error={}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Result.error("生成失败: " + e.getMessage()));
        }
    }

    /**
     * 检查转诊编号是否存在
     */
    @GetMapping("/check-no/{referralNo}")
    @Operation(summary = "检查转诊编号", description = "检查转诊编号是否已存在")
    public Result<Boolean> checkReferralNoExists(
            @Parameter(description = "转诊编号", required = true) @PathVariable @NotNull String referralNo) {
        try {
            log.debug("检查转诊编号是否存在: referralNo={}", referralNo);

            boolean exists = referralApplicationService.isReferralNoExists(referralNo);

            return Result.success("检查成功", exists);

        } catch (Exception e) {
            log.error("检查转诊编号异常: referralNo={}, error={}", referralNo, e.getMessage(), e);
            return Result.error("检查失败: " + e.getMessage());
        }
    }
}