package com.hys.hm.interfaces.web.controller.referral;

import com.hys.hm.application.referral.dto.ReferralQueryDTO;
import com.hys.hm.application.referral.service.ReferralApplicationService;
import com.hys.hm.shared.framework.page.PageRequest;
import com.hys.hm.shared.framework.page.PageResult;
import com.hys.hm.shared.framework.query.QueryCondition;
import com.hys.hm.shared.framework.service.BaseService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * 转诊控制器服务适配器
 * 将ReferralApplicationService适配为BaseService接口
 * 用于BaseController的依赖注入
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-29
 */
@Service
@RequiredArgsConstructor
public class ReferralControllerService implements BaseService<ReferralQueryDTO, String> {
    
    private final ReferralApplicationService referralApplicationService;
    
    @Override
    public ReferralQueryDTO save(ReferralQueryDTO entity) {
        // 这个方法在控制器层不会被直接调用
        // 实际的保存操作通过ReferralApplicationService的createReferralForm方法
        throw new UnsupportedOperationException("请使用ReferralApplicationService.createReferralForm方法");
    }
    
    @Override
    public List<ReferralQueryDTO> saveAll(List<ReferralQueryDTO> entities) {
        throw new UnsupportedOperationException("批量保存操作不支持");
    }
    
    @Override
    public ReferralQueryDTO update(ReferralQueryDTO entity) {
        // 这个方法在控制器层不会被直接调用
        // 实际的更新操作通过具体的业务方法（如confirmReferral等）
        throw new UnsupportedOperationException("请使用具体的业务方法进行更新");
    }
    
    @Override
    public Optional<ReferralQueryDTO> findById(String id) {
        return referralApplicationService.getReferralDetail(id);
    }
    
    @Override
    public List<ReferralQueryDTO> findAll() {
        // 转诊表单通常不提供查询所有的功能，出于性能考虑
        throw new UnsupportedOperationException("请使用具体的查询方法");
    }
    
    @Override
    public List<ReferralQueryDTO> findByConditions(List<QueryCondition> conditions) {
        // 这里可以根据查询条件调用相应的查询方法
        // 暂时不实现，使用具体的查询方法
        throw new UnsupportedOperationException("请使用具体的查询方法");
    }
    
    @Override
    public PageResult<ReferralQueryDTO> findByPageRequest(PageRequest pageRequest) {
        // 这里可以实现分页查询逻辑
        // 暂时不实现，使用具体的查询方法
        throw new UnsupportedOperationException("请使用具体的查询方法");
    }
    
    @Override
    public boolean deleteById(String id) {
        // 转诊表单通常不提供物理删除，而是通过取消操作
        throw new UnsupportedOperationException("请使用cancelReferral方法");
    }
    
    @Override
    public int deleteByIds(List<String> ids) {
        throw new UnsupportedOperationException("批量删除操作不支持");
    }
    
    @Override
    public boolean existsById(String id) {
        return referralApplicationService.getReferralDetail(id).isPresent();
    }
    
    @Override
    public long count() {
        // 可以通过统计接口获取总数
        throw new UnsupportedOperationException("请使用统计接口");
    }
    
    @Override
    public long countByConditions(List<QueryCondition> conditions) {
        throw new UnsupportedOperationException("请使用统计接口");
    }
}
