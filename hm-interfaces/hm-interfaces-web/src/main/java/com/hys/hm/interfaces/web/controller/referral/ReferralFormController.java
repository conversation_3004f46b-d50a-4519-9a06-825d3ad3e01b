package com.hys.hm.interfaces.web.controller.referral;

import com.hys.hm.interfaces.web.vo.referral.ReferralFormCreateVO;
import com.hys.hm.interfaces.web.vo.referral.ReferralFormVO;
import com.hys.hm.interfaces.web.vo.referral.ReferralFormSearchVO;
import com.hys.hm.interfaces.web.vo.referral.ReferralActionVO;
import com.hys.hm.interfaces.web.converter.referral.ReferralFormWebConverter;
import com.hys.hm.application.referral.service.ReferralFormApplicationService;
import com.hys.hm.application.referral.dto.ReferralFormQueryDTO;
import com.hys.hm.application.referral.dto.ReferralFormSearchDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import java.util.Optional;

/**
 * 转诊表单控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-23
 */
@Slf4j
@RestController
@RequestMapping("/api/referral/forms")
@RequiredArgsConstructor
@Validated
@Tag(name = "转诊管理", description = "转诊表单相关接口")
public class ReferralFormController {

    private final ReferralFormApplicationService referralFormApplicationService;
    private final ReferralFormWebConverter referralFormWebConverter;

    /**
     * 创建转诊表单
     */
    @PostMapping
    @Operation(summary = "创建转诊表单", description = "创建新的转诊表单")
    public ReferralFormVO createReferralForm(
            @Valid @RequestBody ReferralFormCreateVO createVO,
            @Parameter(description = "当前用户ID", required = true) @RequestHeader("X-User-Id") String currentUserId) {

        log.info("创建转诊表单，操作人: {}, 患者: {}", currentUserId, createVO.getPatientName());

        // 转换VO为DTO
        var createDTO = referralFormWebConverter.toCreateDTO(createVO);

        // 创建转诊表单
        var resultDTO = referralFormApplicationService.createReferralForm(createDTO, currentUserId);

        // 转换DTO为VO
        return referralFormWebConverter.toVO(resultDTO);
    }

    /**
     * 根据ID查询转诊表单
     */
    @GetMapping("/{id}")
    @Operation(summary = "查询转诊表单", description = "根据ID查询转诊表单详情")
    public ReferralFormVO getReferralForm(
            @Parameter(description = "转诊表单ID", required = true) @PathVariable @NotBlank String id) {

        log.info("查询转诊表单，ID: {}", id);

        Optional<ReferralFormQueryDTO> queryDTO = referralFormApplicationService.findById(id);

        return queryDTO.map(referralFormWebConverter::toVO)
                      .orElse(null);
    }

    /**
     * 根据转诊编号查询转诊表单
     */
    @GetMapping("/by-no/{referralNo}")
    @Operation(summary = "根据转诊编号查询", description = "根据转诊编号查询转诊表单")
    public ReferralFormVO getReferralFormByNo(
            @Parameter(description = "转诊编号", required = true) @PathVariable @NotBlank String referralNo) {

        log.info("根据转诊编号查询转诊表单，编号: {}", referralNo);

        Optional<ReferralFormQueryDTO> queryDTO = referralFormApplicationService.findByReferralNo(referralNo);

        return queryDTO.map(referralFormWebConverter::toVO)
                      .orElse(null);
    }

    /**
     * 搜索转诊表单
     */
    @PostMapping("/search")
    @Operation(summary = "搜索转诊表单", description = "根据条件搜索转诊表单列表")
    public List<ReferralFormVO> searchReferralForms(@Valid @RequestBody ReferralFormSearchVO searchVO) {

        log.info("搜索转诊表单，条件: {}", searchVO);

        // 转换VO为DTO
        ReferralFormSearchDTO searchDTO = referralFormWebConverter.toSearchDTO(searchVO);

        // 搜索转诊表单
        List<ReferralFormQueryDTO> queryDTOs = referralFormApplicationService.searchReferralForms(searchDTO);

        // 转换DTO为VO
        return referralFormWebConverter.toVOList(queryDTOs);
    }

    /**
     * 分页查询转诊表单
     */
    @GetMapping
    @Operation(summary = "分页查询转诊表单", description = "分页查询转诊表单列表")
    public List<ReferralFormVO> getReferralForms(
            @Parameter(description = "页码", example = "1") @RequestParam(defaultValue = "1") int page,
            @Parameter(description = "每页大小", example = "20") @RequestParam(defaultValue = "20") int size) {

        log.info("分页查询转诊表单，页码: {}, 大小: {}", page, size);

        List<ReferralFormQueryDTO> queryDTOs = referralFormApplicationService.findWithPagination(page, size);

        return referralFormWebConverter.toVOList(queryDTOs);
    }

    /**
     * 根据转出医院查询转诊表单
     */
    @GetMapping("/out-hospital/{outUnitId}")
    @Operation(summary = "查询转出转诊表单", description = "根据转出医院查询转诊表单列表")
    public List<ReferralFormVO> getReferralFormsByOutHospital(
            @Parameter(description = "转出医院ID", required = true) @PathVariable @NotBlank String outUnitId) {

        log.info("查询转出转诊表单，医院ID: {}", outUnitId);

        List<ReferralFormQueryDTO> queryDTOs = referralFormApplicationService.findByOutHospital(outUnitId);

        return referralFormWebConverter.toVOList(queryDTOs);
    }

    /**
     * 根据转入医院查询转诊表单
     */
    @GetMapping("/in-hospital/{inUnitId}")
    @Operation(summary = "查询转入转诊表单", description = "根据转入医院查询转诊表单列表")
    public List<ReferralFormVO> getReferralFormsByInHospital(
            @Parameter(description = "转入医院ID", required = true) @PathVariable @NotBlank String inUnitId) {

        log.info("查询转入转诊表单，医院ID: {}", inUnitId);

        List<ReferralFormQueryDTO> queryDTOs = referralFormApplicationService.findByInHospital(inUnitId);

        return referralFormWebConverter.toVOList(queryDTOs);
    }

    /**
     * 根据转出医生查询转诊表单
     */
    @GetMapping("/out-doctor/{outDoctorId}")
    @Operation(summary = "查询医生转出表单", description = "根据转出医生查询转诊表单列表")
    public List<ReferralFormVO> getReferralFormsByOutDoctor(
            @Parameter(description = "转出医生ID", required = true) @PathVariable @NotBlank String outDoctorId) {

        log.info("查询医生转出转诊表单，医生ID: {}", outDoctorId);

        List<ReferralFormQueryDTO> queryDTOs = referralFormApplicationService.findByOutDoctor(outDoctorId);

        return referralFormWebConverter.toVOList(queryDTOs);
    }

    /**
     * 根据转入医生查询转诊表单
     */
    @GetMapping("/in-doctor/{inDoctorId}")
    @Operation(summary = "查询医生转入表单", description = "根据转入医生查询转诊表单列表")
    public List<ReferralFormVO> getReferralFormsByInDoctor(
            @Parameter(description = "转入医生ID", required = true) @PathVariable @NotBlank String inDoctorId) {

        log.info("查询医生转入转诊表单，医生ID: {}", inDoctorId);

        List<ReferralFormQueryDTO> queryDTOs = referralFormApplicationService.findByInDoctor(inDoctorId);

        return referralFormWebConverter.toVOList(queryDTOs);
    }

    /**
     * 根据状态查询转诊表单
     */
    @GetMapping("/status/{status}")
    @Operation(summary = "根据状态查询", description = "根据转诊状态查询转诊表单列表")
    public List<ReferralFormVO> getReferralFormsByStatus(
            @Parameter(description = "转诊状态", required = true) @PathVariable @NotNull Integer status) {

        log.info("根据状态查询转诊表单，状态: {}", status);

        List<ReferralFormQueryDTO> queryDTOs = referralFormApplicationService.findByStatus(status);

        return referralFormWebConverter.toVOList(queryDTOs);
    }

    /**
     * 根据患者姓名模糊查询转诊表单
     */
    @GetMapping("/patient/{patientName}")
    @Operation(summary = "根据患者姓名查询", description = "根据患者姓名模糊查询转诊表单列表")
    public List<ReferralFormVO> getReferralFormsByPatientName(
            @Parameter(description = "患者姓名", required = true) @PathVariable @NotBlank String patientName) {

        log.info("根据患者姓名查询转诊表单，姓名: {}", patientName);

        List<ReferralFormQueryDTO> queryDTOs = referralFormApplicationService.findByPatientNameLike(patientName);

        return referralFormWebConverter.toVOList(queryDTOs);
    }

    /**
     * 确认转诊
     */
    @PutMapping("/{id}/confirm")
    @Operation(summary = "确认转诊", description = "确认接收转诊申请")
    public void confirmReferral(
            @Parameter(description = "转诊表单ID", required = true) @PathVariable @NotBlank String id,
            @Parameter(description = "当前用户ID", required = true) @RequestHeader("X-User-Id") String currentUserId) {

        log.info("确认转诊，转诊ID: {}, 操作人: {}", id, currentUserId);

        referralFormApplicationService.confirmReferral(id, currentUserId);
    }

    /**
     * 拒绝转诊
     */
    @PutMapping("/{id}/reject")
    @Operation(summary = "拒绝转诊", description = "拒绝转诊申请")
    public void rejectReferral(
            @Parameter(description = "转诊表单ID", required = true) @PathVariable @NotBlank String id,
            @Valid @RequestBody ReferralActionVO actionVO,
            @Parameter(description = "当前用户ID", required = true) @RequestHeader("X-User-Id") String currentUserId) {

        log.info("拒绝转诊，转诊ID: {}, 操作人: {}", id, currentUserId);

        if (actionVO.getRejectReason() == null || actionVO.getRejectReason().trim().isEmpty()) {
            throw new IllegalArgumentException("拒绝转诊必须提供拒绝原因");
        }

        referralFormApplicationService.rejectReferral(id, actionVO.getRejectReason(), currentUserId);
    }

    /**
     * 取消转诊
     */
    @PutMapping("/{id}/cancel")
    @Operation(summary = "取消转诊", description = "取消转诊申请")
    public void cancelReferral(
            @Parameter(description = "转诊表单ID", required = true) @PathVariable @NotBlank String id,
            @Parameter(description = "当前用户ID", required = true) @RequestHeader("X-User-Id") String currentUserId) {

        log.info("取消转诊，转诊ID: {}, 操作人: {}", id, currentUserId);

        referralFormApplicationService.cancelReferral(id, currentUserId);
    }

    /**
     * 完成转诊
     */
    @PutMapping("/{id}/complete")
    @Operation(summary = "完成转诊", description = "标记转诊为已完成")
    public void completeReferral(
            @Parameter(description = "转诊表单ID", required = true) @PathVariable @NotBlank String id,
            @Parameter(description = "当前用户ID", required = true) @RequestHeader("X-User-Id") String currentUserId) {

        log.info("完成转诊，转诊ID: {}, 操作人: {}", id, currentUserId);

        referralFormApplicationService.completeReferral(id, currentUserId);
    }

    /**
     * 查询超时的转诊表单
     */
    @GetMapping("/timeout")
    @Operation(summary = "查询超时转诊", description = "查询超时未确认的转诊表单列表")
    public List<ReferralFormVO> getTimeoutReferrals() {

        log.info("查询超时转诊表单");

        List<ReferralFormQueryDTO> queryDTOs = referralFormApplicationService.findTimeoutReferrals();

        return referralFormWebConverter.toVOList(queryDTOs);
    }

    /**
     * 获取医院转诊统计
     */
    @GetMapping("/statistics/hospital/{unitId}")
    @Operation(summary = "医院转诊统计", description = "获取指定医院的转诊统计信息")
    public ReferralFormDomainService.ReferralStatistics getHospitalStatistics(
            @Parameter(description = "医院ID", required = true) @PathVariable @NotBlank String unitId) {

        log.info("获取医院转诊统计，医院ID: {}", unitId);

        return referralFormApplicationService.getHospitalStatistics(unitId);
    }

    /**
     * 统计转诊表单总数
     */
    @GetMapping("/count")
    @Operation(summary = "转诊表单总数", description = "统计转诊表单总数")
    public long countReferralForms() {

        log.info("统计转诊表单总数");

        return referralFormApplicationService.count();
    }

    /**
     * 根据状态统计转诊表单数量
     */
    @GetMapping("/count/status/{status}")
    @Operation(summary = "按状态统计", description = "根据状态统计转诊表单数量")
    public long countReferralFormsByStatus(
            @Parameter(description = "转诊状态", required = true) @PathVariable @NotNull Integer status) {

        log.info("根据状态统计转诊表单数量，状态: {}", status);

        return referralFormApplicationService.countByStatus(status);
    }

    /**
     * 根据身份证号查询转诊表单（支持加密字段）
     */
    @GetMapping("/search/idcard/{idCard}")
    @Operation(summary = "根据身份证号查询", description = "根据身份证号查询转诊表单（支持加密字段模糊查询）")
    public List<ReferralFormVO> getReferralFormsByIdCard(
            @Parameter(description = "身份证号", required = true) @PathVariable @NotBlank String idCard,
            @Parameter(description = "是否精确匹配", example = "false") @RequestParam(defaultValue = "false") boolean exactMatch) {

        log.info("根据身份证号查询转诊表单，身份证号: {}, 精确匹配: {}", maskSensitiveData(idCard), exactMatch);

        List<ReferralFormQueryDTO> queryDTOs = referralFormApplicationService.findByIdCard(idCard, exactMatch);
        return referralFormWebConverter.toVOList(queryDTOs);
    }

    /**
     * 根据手机号查询转诊表单（支持加密字段）
     */
    @GetMapping("/search/phone/{phone}")
    @Operation(summary = "根据手机号查询", description = "根据手机号查询转诊表单（支持加密字段模糊查询）")
    public List<ReferralFormVO> getReferralFormsByPhone(
            @Parameter(description = "手机号", required = true) @PathVariable @NotBlank String phone,
            @Parameter(description = "是否精确匹配", example = "false") @RequestParam(defaultValue = "false") boolean exactMatch) {

        log.info("根据手机号查询转诊表单，手机号: {}, 精确匹配: {}", maskSensitiveData(phone), exactMatch);

        List<ReferralFormQueryDTO> queryDTOs = referralFormApplicationService.findByPhone(phone, exactMatch);
        return referralFormWebConverter.toVOList(queryDTOs);
    }

    /**
     * 脱敏敏感数据用于日志显示
     */
    private String maskSensitiveData(String data) {
        if (data == null || data.length() <= 4) {
            return "****";
        }
        return data.substring(0, 3) + "****" + data.substring(data.length() - 1);
    }
}
