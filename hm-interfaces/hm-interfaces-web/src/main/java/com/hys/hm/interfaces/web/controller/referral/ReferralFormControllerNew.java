package com.hys.hm.interfaces.web.controller.referral;

import com.hys.hm.application.referral.service.ReferralFormServiceNew;
import com.hys.hm.infrastructure.persistence.referral.entity.ReferralFormEntityNew;
import com.hys.hm.shared.common.Result;
import com.hys.hm.shared.framework.controller.BaseController;
import com.hys.hm.shared.framework.page.PageRequest;
import com.hys.hm.shared.framework.page.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 转诊表单控制器（使用新框架）
 * 继承BaseController获得完整的REST API功能
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-29
 */
@Slf4j
@RestController
@RequestMapping("/api/v2/referral-forms")
@Tag(name = "转诊表单管理（新版）", description = "转诊表单的CRUD操作和业务功能（使用新框架）")
public class ReferralFormControllerNew extends BaseController<ReferralFormEntityNew, String> {

    @Autowired
    private ReferralFormServiceNew referralFormService;

    @Override
    protected String getEntityId(ReferralFormEntityNew entity) {
        return entity.getId();
    }

    @Override
    protected void setEntityId(ReferralFormEntityNew entity, String id) {
        entity.setId(id);
    }

    /**
     * 根据转诊编号查找转诊表单
     */
    @GetMapping("/by-referral-no/{referralNo}")
    @Operation(summary = "根据转诊编号查找转诊表单", description = "根据转诊编号精确查找转诊表单")
    public ResponseEntity<Result<ReferralFormEntityNew>> findByReferralNo(
            @Parameter(description = "转诊编号", required = true) @PathVariable @NotNull String referralNo) {
        try {
            Optional<ReferralFormEntityNew> referralOpt = referralFormService.findByReferralNo(referralNo);
            if (referralOpt.isPresent()) {
                return ResponseEntity.ok(Result.success("查询成功", referralOpt.get()));
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            log.error("根据转诊编号查找转诊表单失败: referralNo={}, 错误={}", referralNo, e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Result.error("查询失败: " + e.getMessage()));
        }
    }

    /**
     * 根据基础信息ID查找转诊表单列表
     */
    @GetMapping("/by-basic-info/{basicInfoId}")
    @Operation(summary = "根据基础信息ID查找转诊表单列表", description = "根据基础信息ID查找相关的转诊表单列表")
    public ResponseEntity<Result<List<ReferralFormEntityNew>>> findByBasicInfoId(
            @Parameter(description = "基础信息ID", required = true) @PathVariable @NotNull String basicInfoId) {
        try {
            List<ReferralFormEntityNew> referrals = referralFormService.findByBasicInfoId(basicInfoId);
            return ResponseEntity.ok(Result.success("查询成功", referrals));
        } catch (Exception e) {
            log.error("根据基础信息ID查找转诊表单失败: basicInfoId={}, 错误={}", basicInfoId, e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Result.error("查询失败: " + e.getMessage()));
        }
    }

    /**
     * 根据医院查找转诊表单列表
     */
    @GetMapping("/by-unit/{unitId}")
    @Operation(summary = "根据医院查找转诊表单列表", description = "根据医院ID查找转出或转入的转诊表单列表")
    public ResponseEntity<Result<List<ReferralFormEntityNew>>> findByUnitId(
            @Parameter(description = "医院ID", required = true) @PathVariable @NotNull String unitId,
            @Parameter(description = "是否为转出医院", example = "true") @RequestParam(defaultValue = "true") boolean isOutUnit) {
        try {
            List<ReferralFormEntityNew> referrals;
            if (isOutUnit) {
                referrals = referralFormService.findByOutUnitId(unitId);
            } else {
                referrals = referralFormService.findByInUnitId(unitId);
            }
            return ResponseEntity.ok(Result.success("查询成功", referrals));
        } catch (Exception e) {
            log.error("根据医院查找转诊表单失败: unitId={}, isOutUnit={}, 错误={}", unitId, isOutUnit, e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Result.error("查询失败: " + e.getMessage()));
        }
    }

    /**
     * 根据医生查找转诊表单列表
     */
    @GetMapping("/by-doctor/{doctorId}")
    @Operation(summary = "根据医生查找转诊表单列表", description = "根据医生ID查找转出或转入的转诊表单列表")
    public ResponseEntity<Result<List<ReferralFormEntityNew>>> findByDoctorId(
            @Parameter(description = "医生ID", required = true) @PathVariable @NotNull String doctorId) {
        try {
            List<ReferralFormEntityNew> referrals = referralFormService.findByDoctorId(doctorId);
            return ResponseEntity.ok(Result.success("查询成功", referrals));
        } catch (Exception e) {
            log.error("根据医生查找转诊表单失败: doctorId={}, 错误={}", doctorId, e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Result.error("查询失败: " + e.getMessage()));
        }
    }

    /**
     * 根据状态查找转诊表单列表
     */
    @GetMapping("/by-status/{status}")
    @Operation(summary = "根据状态查找转诊表单列表", description = "根据转诊状态查找转诊表单列表")
    public ResponseEntity<Result<List<ReferralFormEntityNew>>> findByStatus(
            @Parameter(description = "转诊状态", required = true) @PathVariable @NotNull Integer status) {
        try {
            List<ReferralFormEntityNew> referrals = referralFormService.findByStatus(status);
            return ResponseEntity.ok(Result.success("查询成功", referrals));
        } catch (Exception e) {
            log.error("根据状态查找转诊表单失败: status={}, 错误={}", status, e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Result.error("查询失败: " + e.getMessage()));
        }
    }

    /**
     * 根据患者姓名模糊查找转诊表单列表
     */
    @GetMapping("/search-by-patient/{patientName}")
    @Operation(summary = "根据患者姓名模糊查找转诊表单列表", description = "根据患者姓名关键字模糊查找转诊表单列表")
    public ResponseEntity<Result<List<ReferralFormEntityNew>>> searchByPatientName(
            @Parameter(description = "患者姓名关键字", required = true) @PathVariable @NotNull String patientName) {
        try {
            List<ReferralFormEntityNew> referrals = referralFormService.findByPatientNameLike(patientName);
            return ResponseEntity.ok(Result.success("查询成功", referrals));
        } catch (Exception e) {
            log.error("根据患者姓名模糊查找转诊表单失败: patientName={}, 错误={}", patientName, e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Result.error("查询失败: " + e.getMessage()));
        }
    }

    /**
     * 根据身份证号查找转诊表单列表
     */
    @GetMapping("/search-by-idcard/{idCard}")
    @Operation(summary = "根据身份证号查找转诊表单列表", description = "根据身份证号查找转诊表单列表（支持加密字段）")
    public ResponseEntity<Result<List<ReferralFormEntityNew>>> searchByIdCard(
            @Parameter(description = "身份证号", required = true) @PathVariable @NotNull String idCard) {
        try {
            List<ReferralFormEntityNew> referrals = referralFormService.findByIdCard(idCard);
            return ResponseEntity.ok(Result.success("查询成功", referrals));
        } catch (Exception e) {
            log.error("根据身份证号查找转诊表单失败: idCard={}, 错误={}", idCard, e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Result.error("查询失败: " + e.getMessage()));
        }
    }

    /**
     * 根据手机号查找转诊表单列表
     */
    @GetMapping("/search-by-phone/{phone}")
    @Operation(summary = "根据手机号查找转诊表单列表", description = "根据手机号查找转诊表单列表（支持加密字段）")
    public ResponseEntity<Result<List<ReferralFormEntityNew>>> searchByPhone(
            @Parameter(description = "手机号", required = true) @PathVariable @NotNull String phone) {
        try {
            List<ReferralFormEntityNew> referrals = referralFormService.findByPhone(phone);
            return ResponseEntity.ok(Result.success("查询成功", referrals));
        } catch (Exception e) {
            log.error("根据手机号查找转诊表单失败: phone={}, 错误={}", phone, e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Result.error("查询失败: " + e.getMessage()));
        }
    }

    /**
     * 查找待处理的转诊表单
     */
    @GetMapping("/pending")
    @Operation(summary = "查找待处理的转诊表单", description = "查找所有待处理状态的转诊表单")
    public ResponseEntity<Result<List<ReferralFormEntityNew>>> findPendingReferrals() {
        try {
            List<ReferralFormEntityNew> referrals = referralFormService.findPendingReferrals();
            return ResponseEntity.ok(Result.success("查询成功", referrals));
        } catch (Exception e) {
            log.error("查找待处理转诊表单失败: 错误={}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Result.error("查询失败: " + e.getMessage()));
        }
    }

    /**
     * 查找紧急转诊表单
     */
    @GetMapping("/urgent")
    @Operation(summary = "查找紧急转诊表单", description = "查找所有紧急和急诊的转诊表单")
    public ResponseEntity<Result<List<ReferralFormEntityNew>>> findUrgentReferrals() {
        try {
            List<ReferralFormEntityNew> referrals = referralFormService.findUrgentReferrals();
            return ResponseEntity.ok(Result.success("查询成功", referrals));
        } catch (Exception e) {
            log.error("查找紧急转诊表单失败: 错误={}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Result.error("查询失败: " + e.getMessage()));
        }
    }

    /**
     * 查找超时转诊表单
     */
    @GetMapping("/timeout")
    @Operation(summary = "查找超时转诊表单", description = "查找超过指定天数未处理的转诊表单")
    public ResponseEntity<Result<List<ReferralFormEntityNew>>> findTimeoutReferrals(
            @Parameter(description = "超时天数", example = "7") @RequestParam(defaultValue = "7") int timeoutDays) {
        try {
            List<ReferralFormEntityNew> referrals = referralFormService.findTimeoutReferrals(timeoutDays);
            return ResponseEntity.ok(Result.success("查询成功", referrals));
        } catch (Exception e) {
            log.error("查找超时转诊表单失败: timeoutDays={}, 错误={}", timeoutDays, e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Result.error("查询失败: " + e.getMessage()));
        }
    }

    /**
     * 查找今日转诊表单
     */
    @GetMapping("/today")
    @Operation(summary = "查找今日转诊表单", description = "查找今天创建的转诊表单")
    public ResponseEntity<Result<List<ReferralFormEntityNew>>> findTodayReferrals() {
        try {
            List<ReferralFormEntityNew> referrals = referralFormService.findTodayReferrals();
            return ResponseEntity.ok(Result.success("查询成功", referrals));
        } catch (Exception e) {
            log.error("查找今日转诊表单失败: 错误={}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Result.error("查询失败: " + e.getMessage()));
        }
    }

    /**
     * 确认转诊
     */
    @PutMapping("/{id}/confirm")
    @Operation(summary = "确认转诊", description = "确认转诊申请")
    public ResponseEntity<Result<Void>> confirmReferral(
            @Parameter(description = "转诊表单ID", required = true) @PathVariable @NotNull String id,
            @Parameter(description = "当前用户ID", required = true) @RequestHeader("X-User-Id") String currentUserId) {
        try {
            boolean success = referralFormService.confirmReferral(id, currentUserId);
            if (success) {
                return ResponseEntity.ok(Result.success("确认成功"));
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (IllegalStateException e) {
            log.warn("确认转诊失败: id={}, 错误={}", id, e.getMessage());
            return ResponseEntity.badRequest().body(Result.error(Result.PARAM_ERROR_CODE, e.getMessage()));
        } catch (Exception e) {
            log.error("确认转诊异常: id={}, 错误={}", id, e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Result.error("确认失败: " + e.getMessage()));
        }
    }

    /**
     * 拒绝转诊
     */
    @PutMapping("/{id}/reject")
    @Operation(summary = "拒绝转诊", description = "拒绝转诊申请")
    public ResponseEntity<Result<Void>> rejectReferral(
            @Parameter(description = "转诊表单ID", required = true) @PathVariable @NotNull String id,
            @Parameter(description = "拒绝原因", required = true) @RequestParam @NotNull String rejectReason,
            @Parameter(description = "当前用户ID", required = true) @RequestHeader("X-User-Id") String currentUserId) {
        try {
            boolean success = referralFormService.rejectReferral(id, rejectReason, currentUserId);
            if (success) {
                return ResponseEntity.ok(Result.success("拒绝成功"));
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (IllegalStateException e) {
            log.warn("拒绝转诊失败: id={}, 错误={}", id, e.getMessage());
            return ResponseEntity.badRequest().body(Result.error(Result.PARAM_ERROR_CODE, e.getMessage()));
        } catch (Exception e) {
            log.error("拒绝转诊异常: id={}, 错误={}", id, e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Result.error("拒绝失败: " + e.getMessage()));
        }
    }

    /**
     * 取消转诊
     */
    @PutMapping("/{id}/cancel")
    @Operation(summary = "取消转诊", description = "取消转诊申请")
    public ResponseEntity<Result<Void>> cancelReferral(
            @Parameter(description = "转诊表单ID", required = true) @PathVariable @NotNull String id,
            @Parameter(description = "当前用户ID", required = true) @RequestHeader("X-User-Id") String currentUserId) {
        try {
            boolean success = referralFormService.cancelReferral(id, currentUserId);
            if (success) {
                return ResponseEntity.ok(Result.success("取消成功"));
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (IllegalStateException e) {
            log.warn("取消转诊失败: id={}, 错误={}", id, e.getMessage());
            return ResponseEntity.badRequest().body(Result.error(Result.PARAM_ERROR_CODE, e.getMessage()));
        } catch (Exception e) {
            log.error("取消转诊异常: id={}, 错误={}", id, e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Result.error("取消失败: " + e.getMessage()));
        }
    }

    /**
     * 完成转诊
     */
    @PutMapping("/{id}/complete")
    @Operation(summary = "完成转诊", description = "完成转诊流程")
    public ResponseEntity<Result<Void>> completeReferral(
            @Parameter(description = "转诊表单ID", required = true) @PathVariable @NotNull String id,
            @Parameter(description = "当前用户ID", required = true) @RequestHeader("X-User-Id") String currentUserId) {
        try {
            boolean success = referralFormService.completeReferral(id, currentUserId);
            if (success) {
                return ResponseEntity.ok(Result.success("完成成功"));
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (IllegalStateException e) {
            log.warn("完成转诊失败: id={}, 错误={}", id, e.getMessage());
            return ResponseEntity.badRequest().body(Result.error(Result.PARAM_ERROR_CODE, e.getMessage()));
        } catch (Exception e) {
            log.error("完成转诊异常: id={}, 错误={}", id, e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Result.error("完成失败: " + e.getMessage()));
        }
    }

    /**
     * 批量确认转诊
     */
    @PutMapping("/batch-confirm")
    @Operation(summary = "批量确认转诊", description = "批量确认多个转诊申请")
    public ResponseEntity<Result<Integer>> batchConfirmReferrals(
            @Valid @RequestBody @NotEmpty List<String> ids,
            @Parameter(description = "当前用户ID", required = true) @RequestHeader("X-User-Id") String currentUserId) {
        try {
            int successCount = referralFormService.batchConfirmReferrals(ids, currentUserId);
            return ResponseEntity.ok(Result.success("批量确认成功", successCount));
        } catch (Exception e) {
            log.error("批量确认转诊异常: ids={}, 错误={}", ids, e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Result.error("批量确认失败: " + e.getMessage()));
        }
    }

    /**
     * 批量取消转诊
     */
    @PutMapping("/batch-cancel")
    @Operation(summary = "批量取消转诊", description = "批量取消多个转诊申请")
    public ResponseEntity<Result<Integer>> batchCancelReferrals(
            @Valid @RequestBody @NotEmpty List<String> ids,
            @Parameter(description = "当前用户ID", required = true) @RequestHeader("X-User-Id") String currentUserId) {
        try {
            int successCount = referralFormService.batchCancelReferrals(ids, currentUserId);
            return ResponseEntity.ok(Result.success("批量取消成功", successCount));
        } catch (Exception e) {
            log.error("批量取消转诊异常: ids={}, 错误={}", ids, e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Result.error("批量取消失败: " + e.getMessage()));
        }
    }

    /**
     * 获取转诊统计信息
     */
    @GetMapping("/statistics")
    @Operation(summary = "获取转诊统计信息", description = "获取转诊表单的统计信息")
    public ResponseEntity<Result<Map<String, Object>>> getReferralStatistics(
            @Parameter(description = "医院ID（可选）") @RequestParam(required = false) String unitId,
            @Parameter(description = "开始时间（可选）") @RequestParam(required = false) LocalDateTime startDate,
            @Parameter(description = "结束时间（可选）") @RequestParam(required = false) LocalDateTime endDate) {
        try {
            Map<String, Object> statistics = referralFormService.getReferralStatistics(unitId, startDate, endDate);
            return ResponseEntity.ok(Result.success("统计成功", statistics));
        } catch (Exception e) {
            log.error("获取转诊统计信息异常: unitId={}, startDate={}, endDate={}, 错误={}",
                    unitId, startDate, endDate, e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Result.error("统计失败: " + e.getMessage()));
        }
    }

    /**
     * 导出转诊表单数据
     */
    @GetMapping("/export")
    @Operation(summary = "导出转诊表单数据", description = "导出转诊表单数据，支持多种过滤条件")
    public ResponseEntity<Result<List<ReferralFormEntityNew>>> exportReferralForms(
            @Parameter(description = "开始时间（可选）") @RequestParam(required = false) LocalDateTime startDate,
            @Parameter(description = "结束时间（可选）") @RequestParam(required = false) LocalDateTime endDate,
            @Parameter(description = "状态（可选）") @RequestParam(required = false) Integer status,
            @Parameter(description = "医院ID（可选）") @RequestParam(required = false) String unitId) {
        try {
            List<ReferralFormEntityNew> referrals = referralFormService.exportReferralForms(
                    startDate, endDate, status, unitId);
            return ResponseEntity.ok(Result.success("导出成功", referrals));
        } catch (Exception e) {
            log.error("导出转诊表单数据异常: startDate={}, endDate={}, status={}, unitId={}, 错误={}",
                    startDate, endDate, status, unitId, e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Result.error("导出失败: " + e.getMessage()));
        }
    }

    /**
     * 导入转诊表单数据
     */
    @PostMapping("/import")
    @Operation(summary = "导入转诊表单数据", description = "批量导入转诊表单数据")
    public ResponseEntity<Result<Integer>> importReferralForms(
            @Valid @RequestBody @NotEmpty List<ReferralFormEntityNew> referrals,
            @Parameter(description = "当前用户ID", required = true) @RequestHeader("X-User-Id") String currentUserId) {
        try {
            int successCount = referralFormService.importReferralForms(referrals, currentUserId);
            return ResponseEntity.ok(Result.success("导入成功", successCount));
        } catch (Exception e) {
            log.error("导入转诊表单数据异常: 数量={}, 错误={}", referrals.size(), e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Result.error("导入失败: " + e.getMessage()));
        }
    }

    /**
     * 清理过期数据
     */
    @DeleteMapping("/cleanup")
    @Operation(summary = "清理过期数据", description = "清理指定天数之前的过期转诊数据")
    public ResponseEntity<Result<Integer>> cleanupExpiredData(
            @Parameter(description = "保留天数", required = true) @RequestParam @NotNull Integer days,
            @Parameter(description = "当前用户ID", required = true) @RequestHeader("X-User-Id") String currentUserId) {
        try {
            int cleanedCount = referralFormService.cleanupExpiredData(days);
            log.info("清理过期转诊数据: 保留天数={}, 清理数量={}, 操作人={}", days, cleanedCount, currentUserId);
            return ResponseEntity.ok(Result.success("清理成功", cleanedCount));
        } catch (Exception e) {
            log.error("清理过期数据异常: days={}, 错误={}", days, e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Result.error("清理失败: " + e.getMessage()));
        }
    }

    /**
     * 自动处理超时转诊
     */
    @PutMapping("/auto-process-timeout")
    @Operation(summary = "自动处理超时转诊", description = "自动处理超过指定天数未处理的转诊")
    public ResponseEntity<Result<Integer>> autoProcessTimeoutReferrals(
            @Parameter(description = "超时天数", required = true) @RequestParam @NotNull Integer timeoutDays,
            @Parameter(description = "当前用户ID", required = true) @RequestHeader("X-User-Id") String currentUserId) {
        try {
            int processedCount = referralFormService.autoProcessTimeoutReferrals(timeoutDays);
            log.info("自动处理超时转诊: 超时天数={}, 处理数量={}, 操作人={}", timeoutDays, processedCount, currentUserId);
            return ResponseEntity.ok(Result.success("处理成功", processedCount));
        } catch (Exception e) {
            log.error("自动处理超时转诊异常: timeoutDays={}, 错误={}", timeoutDays, e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Result.error("处理失败: " + e.getMessage()));
        }
    }

    /**
     * 生成转诊编号
     */
    @GetMapping("/generate-referral-no")
    @Operation(summary = "生成转诊编号", description = "生成新的转诊编号")
    public ResponseEntity<Result<String>> generateReferralNo() {
        try {
            String referralNo = referralFormService.generateReferralNo();
            return ResponseEntity.ok(Result.success("生成成功", referralNo));
        } catch (Exception e) {
            log.error("生成转诊编号异常: 错误={}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Result.error("生成失败: " + e.getMessage()));
        }
    }

    /**
     * 检查转诊编号是否存在
     */
    @GetMapping("/check-referral-no/{referralNo}")
    @Operation(summary = "检查转诊编号是否存在", description = "检查指定的转诊编号是否已存在")
    public ResponseEntity<Result<Boolean>> checkReferralNoExists(
            @Parameter(description = "转诊编号", required = true) @PathVariable @NotNull String referralNo) {
        try {
            boolean exists = referralFormService.isReferralNoExists(referralNo);
            return ResponseEntity.ok(Result.success("检查成功", exists));
        } catch (Exception e) {
            log.error("检查转诊编号异常: referralNo={}, 错误={}", referralNo, e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Result.error("检查失败: " + e.getMessage()));
        }
    }
}
