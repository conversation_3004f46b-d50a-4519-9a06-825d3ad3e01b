package com.hys.hm.interfaces.web.converter.referral;

import com.hys.hm.interfaces.web.vo.referral.ReferralFormCreateVO;
import com.hys.hm.interfaces.web.vo.referral.ReferralFormVO;
import com.hys.hm.interfaces.web.vo.referral.ReferralFormSearchVO;
import com.hys.hm.application.referral.dto.ReferralFormCreateDTO;
import com.hys.hm.application.referral.dto.ReferralFormQueryDTO;
import com.hys.hm.application.referral.dto.ReferralFormSearchDTO;
import com.hys.hm.shared.common.util.DataMaskingUtil;
import org.springframework.stereotype.Component;

import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 转诊表单Web层转换器
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-23
 */
@Component
public class ReferralFormWebConverter {
    
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
    
    /**
     * 创建VO转换为创建DTO
     */
    public ReferralFormCreateDTO toCreateDTO(ReferralFormCreateVO createVO) {
        if (createVO == null) {
            return null;
        }
        
        ReferralFormCreateDTO createDTO = new ReferralFormCreateDTO();
        
        // 基础信息
        createDTO.setBasicInfoId(createVO.getBasicInfoId());
        
        // 患者信息
        createDTO.setPatientName(createVO.getPatientName());
        createDTO.setGender(createVO.getGender());
        createDTO.setAge(createVO.getAge());
        createDTO.setIdCard(createVO.getIdCard());
        createDTO.setPhone(createVO.getPhone());
        createDTO.setAddress(createVO.getAddress());
        createDTO.setAddressDetail(createVO.getAddressDetail());
        createDTO.setFileNumber(createVO.getFileNumber());
        
        // 医疗信息
        createDTO.setImpression(createVO.getImpression());
        createDTO.setMainHistory(createVO.getMainHistory());
        createDTO.setPastHistory(createVO.getPastHistory());
        createDTO.setTreatmentProcess(createVO.getTreatmentProcess());
        
        // 转出医院信息
        createDTO.setOutUnitId(createVO.getOutUnitId());
        createDTO.setOutUnitName(createVO.getOutUnitName());
        createDTO.setOutDeptId(createVO.getOutDeptId());
        createDTO.setOutDeptName(createVO.getOutDeptName());
        createDTO.setOutDoctorId(createVO.getOutDoctorId());
        createDTO.setOutDoctorName(createVO.getOutDoctorName());
        createDTO.setOutDoctorPhone(createVO.getOutDoctorPhone());
        
        // 转入医院信息
        createDTO.setInUnitId(createVO.getInUnitId());
        createDTO.setInUnitName(createVO.getInUnitName());
        createDTO.setInDeptId(createVO.getInDeptId());
        createDTO.setInDeptName(createVO.getInDeptName());
        createDTO.setInDoctorId(createVO.getInDoctorId());
        createDTO.setInDoctorName(createVO.getInDoctorName());
        createDTO.setInDoctorPhone(createVO.getInDoctorPhone());
        
        return createDTO;
    }
    
    /**
     * 搜索VO转换为搜索DTO
     */
    public ReferralFormSearchDTO toSearchDTO(ReferralFormSearchVO searchVO) {
        if (searchVO == null) {
            return null;
        }
        
        ReferralFormSearchDTO searchDTO = new ReferralFormSearchDTO();
        searchDTO.setReferralNo(searchVO.getReferralNo());
        searchDTO.setPatientName(searchVO.getPatientName());
        searchDTO.setOutUnitId(searchVO.getOutUnitId());
        searchDTO.setInUnitId(searchVO.getInUnitId());
        searchDTO.setOutDoctorId(searchVO.getOutDoctorId());
        searchDTO.setInDoctorId(searchVO.getInDoctorId());
        searchDTO.setStatus(searchVO.getStatus());
        searchDTO.setStartDate(searchVO.getStartDate());
        searchDTO.setEndDate(searchVO.getEndDate());
        searchDTO.setPage(searchVO.getPage());
        searchDTO.setSize(searchVO.getSize());
        searchDTO.setSortBy(searchVO.getSortBy());
        searchDTO.setSortDirection(searchVO.getSortDirection());
        
        return searchDTO;
    }
    
    /**
     * 查询DTO转换为VO
     */
    public ReferralFormVO toVO(ReferralFormQueryDTO queryDTO) {
        if (queryDTO == null) {
            return null;
        }
        
        ReferralFormVO vo = new ReferralFormVO();
        
        // 基本信息
        vo.setId(queryDTO.getId());
        vo.setBasicInfoId(queryDTO.getBasicInfoId());
        vo.setReferralNo(queryDTO.getReferralNo());
        vo.setReferralDate(queryDTO.getReferralDate());
        vo.setReferralDateFormatted(formatDateTime(queryDTO.getReferralDate()));
        vo.setCreateTime(queryDTO.getCreateTime());
        vo.setCreateTimeFormatted(formatDateTime(queryDTO.getCreateTime()));
        
        // 患者信息
        vo.setPatientName(queryDTO.getPatientName());
        vo.setGender(queryDTO.getGender());
        vo.setGenderDesc(queryDTO.getGenderDesc());
        vo.setAge(queryDTO.getAge());
        vo.setAgeDesc(queryDTO.getAge() != null ? queryDTO.getAge() + "岁" : null);
        vo.setMaskedIdCard(queryDTO.getMaskedIdCard());
        vo.setMaskedPhone(queryDTO.getMaskedPhone());
        vo.setFullAddress(queryDTO.getFullAddress());
        vo.setFileNumber(queryDTO.getFileNumber());
        
        // 医疗信息
        vo.setImpression(queryDTO.getImpression());
        vo.setHistorySummary(queryDTO.getHistorySummary());
        vo.setHasTreatmentRecord(queryDTO.getHasTreatmentRecord());
        
        // 转出医院信息
        vo.setOutUnitId(queryDTO.getOutUnitId());
        vo.setOutHospitalInfo(queryDTO.getOutHospitalInfo());
        vo.setOutDoctorInfo(queryDTO.getOutDoctorInfo());
        
        // 转入医院信息
        vo.setInUnitId(queryDTO.getInUnitId());
        vo.setInHospitalInfo(queryDTO.getInHospitalInfo());
        vo.setInDoctorInfo(queryDTO.getInDoctorInfo());
        
        // 状态信息
        vo.setStatus(queryDTO.getStatus());
        vo.setStatusDesc(queryDTO.getStatusDesc());
        vo.setStatusTag(getStatusTag(queryDTO.getStatus()));
        vo.setRejectReason(queryDTO.getRejectReason());
        vo.setConfirmTime(queryDTO.getConfirmTime());
        vo.setConfirmTimeFormatted(formatDateTime(queryDTO.getConfirmTime()));
        
        // 业务状态
        vo.setIsPending(queryDTO.getIsPending());
        vo.setIsConfirmed(queryDTO.getIsConfirmed());
        vo.setIsCompleted(queryDTO.getIsCompleted());
        vo.setIsRejected(queryDTO.getIsRejected());
        vo.setIsCancelled(queryDTO.getIsCancelled());
        vo.setIsTimeout(queryDTO.getIsTimeout());
        vo.setReferralDescription(queryDTO.getReferralDescription());
        
        // 操作权限
        vo.setCanConfirm(queryDTO.getIsPending());
        vo.setCanReject(queryDTO.getIsPending());
        vo.setCanCancel(queryDTO.getIsPending() || queryDTO.getIsConfirmed());
        vo.setCanComplete(queryDTO.getIsConfirmed());
        
        return vo;
    }
    
    /**
     * 查询DTO列表转换为VO列表
     */
    public List<ReferralFormVO> toVOList(List<ReferralFormQueryDTO> queryDTOs) {
        if (queryDTOs == null) {
            return null;
        }
        
        return queryDTOs.stream()
                .map(this::toVO)
                .collect(Collectors.toList());
    }
    
    /**
     * 格式化日期时间
     */
    private String formatDateTime(java.time.LocalDateTime dateTime) {
        return dateTime != null ? dateTime.format(DATE_TIME_FORMATTER) : null;
    }
    
    /**
     * 获取状态标签样式
     */
    private String getStatusTag(Integer status) {
        if (status == null) {
            return "default";
        }
        
        switch (status) {
            case 0: // 待确认
                return "warning";
            case 1: // 已确认
                return "processing";
            case 2: // 已拒绝
                return "error";
            case 3: // 已取消
                return "default";
            case 4: // 已完成
                return "success";
            default:
                return "default";
        }
    }
}
