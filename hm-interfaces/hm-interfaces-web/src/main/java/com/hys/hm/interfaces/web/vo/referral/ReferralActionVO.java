package com.hys.hm.interfaces.web.vo.referral;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * 转诊操作视图对象
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-23
 */
@Data
@Schema(description = "转诊操作请求")
public class ReferralActionVO {
    
    /**
     * 转诊表单ID
     */
    @NotBlank(message = "转诊表单ID不能为空")
    @Schema(description = "转诊表单ID", example = "ref123", required = true)
    private String referralId;
    
    /**
     * 拒绝原因（拒绝操作时必填）
     */
    @Schema(description = "拒绝原因", example = "床位不足，暂时无法接收")
    private String rejectReason;
    
    /**
     * 操作备注
     */
    @Schema(description = "操作备注", example = "已联系患者确认")
    private String remark;
}
