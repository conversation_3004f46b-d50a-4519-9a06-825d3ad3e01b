package com.hys.hm.interfaces.web.vo.referral;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Pattern;

/**
 * 创建转诊表单视图对象
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-23
 */
@Data
@Schema(description = "创建转诊表单请求")
public class ReferralFormCreateVO {
    
    /**
     * 基础信息ID
     */
    @NotBlank(message = "基础信息ID不能为空")
    @Schema(description = "基础信息ID", example = "basic123", required = true)
    private String basicInfoId;
    
    // ========== 患者信息 ==========
    
    /**
     * 患者姓名
     */
    @NotBlank(message = "患者姓名不能为空")
    @Schema(description = "患者姓名", example = "张三", required = true)
    private String patientName;
    
    /**
     * 性别 (1-男, 2-女, 0-未知)
     */
    @NotNull(message = "性别不能为空")
    @Min(value = 0, message = "性别值不正确")
    @Max(value = 2, message = "性别值不正确")
    @Schema(description = "性别", example = "1", required = true, allowableValues = {"0", "1", "2"})
    private Integer gender;
    
    /**
     * 年龄
     */
    @NotNull(message = "年龄不能为空")
    @Min(value = 0, message = "年龄不能小于0")
    @Max(value = 150, message = "年龄不能大于150")
    @Schema(description = "年龄", example = "35", required = true)
    private Integer age;
    
    /**
     * 身份证号
     */
    @Pattern(regexp = "^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$", 
             message = "身份证号格式不正确")
    @Schema(description = "身份证号", example = "110101199001011234")
    private String idCard;
    
    /**
     * 联系电话
     */
    @NotBlank(message = "联系电话不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    @Schema(description = "联系电话", example = "13800138000", required = true)
    private String phone;
    
    /**
     * 地址
     */
    @Schema(description = "地址", example = "北京市朝阳区")
    private String address;
    
    /**
     * 详细地址
     */
    @Schema(description = "详细地址", example = "某某小区1号楼2单元301室")
    private String addressDetail;
    
    /**
     * 档案编号
     */
    @Schema(description = "档案编号", example = "DA20250723001")
    private String fileNumber;
    
    // ========== 医疗信息 ==========
    
    /**
     * 初步诊断
     */
    @NotBlank(message = "初步诊断不能为空")
    @Schema(description = "初步诊断", example = "高血压病", required = true)
    private String impression;
    
    /**
     * 主要病史
     */
    @NotBlank(message = "主要病史不能为空")
    @Schema(description = "主要病史", example = "患者有高血压病史5年", required = true)
    private String mainHistory;
    
    /**
     * 既往病史
     */
    @Schema(description = "既往病史", example = "无特殊既往病史")
    private String pastHistory;
    
    /**
     * 治疗过程
     */
    @Schema(description = "治疗过程", example = "已给予降压药物治疗")
    private String treatmentProcess;
    
    // ========== 转出医院信息 ==========
    
    /**
     * 转出医院ID
     */
    @NotBlank(message = "转出医院ID不能为空")
    @Schema(description = "转出医院ID", example = "hospital001", required = true)
    private String outUnitId;
    
    /**
     * 转出医院名称
     */
    @NotBlank(message = "转出医院名称不能为空")
    @Schema(description = "转出医院名称", example = "某某社区卫生服务中心", required = true)
    private String outUnitName;
    
    /**
     * 转出科室ID
     */
    @Schema(description = "转出科室ID", example = "dept001")
    private String outDeptId;
    
    /**
     * 转出科室名称
     */
    @Schema(description = "转出科室名称", example = "内科")
    private String outDeptName;
    
    /**
     * 转出医生ID
     */
    @NotBlank(message = "转出医生ID不能为空")
    @Schema(description = "转出医生ID", example = "doctor001", required = true)
    private String outDoctorId;
    
    /**
     * 转出医生姓名
     */
    @NotBlank(message = "转出医生姓名不能为空")
    @Schema(description = "转出医生姓名", example = "李医生", required = true)
    private String outDoctorName;
    
    /**
     * 转出医生电话
     */
    @Schema(description = "转出医生电话", example = "13900139000")
    private String outDoctorPhone;
    
    // ========== 转入医院信息 ==========
    
    /**
     * 转入医院ID
     */
    @NotBlank(message = "转入医院ID不能为空")
    @Schema(description = "转入医院ID", example = "hospital002", required = true)
    private String inUnitId;
    
    /**
     * 转入医院名称
     */
    @NotBlank(message = "转入医院名称不能为空")
    @Schema(description = "转入医院名称", example = "某某人民医院", required = true)
    private String inUnitName;
    
    /**
     * 转入科室ID
     */
    @Schema(description = "转入科室ID", example = "dept002")
    private String inDeptId;
    
    /**
     * 转入科室名称
     */
    @Schema(description = "转入科室名称", example = "心血管内科")
    private String inDeptName;
    
    /**
     * 转入医生ID
     */
    @NotBlank(message = "转入医生ID不能为空")
    @Schema(description = "转入医生ID", example = "doctor002", required = true)
    private String inDoctorId;
    
    /**
     * 转入医生姓名
     */
    @NotBlank(message = "转入医生姓名不能为空")
    @Schema(description = "转入医生姓名", example = "王医生", required = true)
    private String inDoctorName;
    
    /**
     * 转入医生电话
     */
    @Schema(description = "转入医生电话", example = "13800138001")
    private String inDoctorPhone;
}
