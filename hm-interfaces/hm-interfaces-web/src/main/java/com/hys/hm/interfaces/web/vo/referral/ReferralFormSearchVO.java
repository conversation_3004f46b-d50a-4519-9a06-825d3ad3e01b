package com.hys.hm.interfaces.web.vo.referral;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 转诊表单搜索条件视图对象
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-23
 */
@Data
@Schema(description = "转诊表单搜索条件")
public class ReferralFormSearchVO {
    
    /**
     * 转诊编号
     */
    @Schema(description = "转诊编号", example = "ZZ20250723001")
    private String referralNo;
    
    /**
     * 患者姓名（模糊搜索）
     */
    @Schema(description = "患者姓名", example = "张三")
    private String patientName;
    
    /**
     * 转出医院ID
     */
    @Schema(description = "转出医院ID", example = "hospital001")
    private String outUnitId;
    
    /**
     * 转入医院ID
     */
    @Schema(description = "转入医院ID", example = "hospital002")
    private String inUnitId;
    
    /**
     * 转出医生ID
     */
    @Schema(description = "转出医生ID", example = "doctor001")
    private String outDoctorId;
    
    /**
     * 转入医生ID
     */
    @Schema(description = "转入医生ID", example = "doctor002")
    private String inDoctorId;
    
    /**
     * 转诊状态
     */
    @Schema(description = "转诊状态", example = "0", allowableValues = {"0", "1", "2", "3", "4"})
    private Integer status;
    
    /**
     * 开始日期
     */
    @Schema(description = "开始日期", example = "2025-07-01T00:00:00")
    private LocalDateTime startDate;
    
    /**
     * 结束日期
     */
    @Schema(description = "结束日期", example = "2025-07-31T23:59:59")
    private LocalDateTime endDate;
    
    /**
     * 页码
     */
    @Schema(description = "页码", example = "1")
    private Integer page = 1;
    
    /**
     * 每页大小
     */
    @Schema(description = "每页大小", example = "20")
    private Integer size = 20;
    
    /**
     * 排序字段
     */
    @Schema(description = "排序字段", example = "referralDate", 
            allowableValues = {"referralDate", "createTime", "confirmTime", "patientName"})
    private String sortBy = "referralDate";
    
    /**
     * 排序方向
     */
    @Schema(description = "排序方向", example = "DESC", allowableValues = {"ASC", "DESC"})
    private String sortDirection = "DESC";
}
