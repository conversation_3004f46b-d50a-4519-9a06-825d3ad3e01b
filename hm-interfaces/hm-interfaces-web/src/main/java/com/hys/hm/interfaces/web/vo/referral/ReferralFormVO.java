package com.hys.hm.interfaces.web.vo.referral;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import com.hys.hm.shared.common.util.DataMaskingUtil;

import java.time.LocalDateTime;

/**
 * 转诊表单视图对象
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-23
 */
@Data
@Schema(description = "转诊表单信息")
public class ReferralFormVO {
    
    /**
     * 转诊表单ID
     */
    @Schema(description = "转诊表单ID", example = "ref123")
    private String id;
    
    /**
     * 基础信息ID
     */
    @Schema(description = "基础信息ID", example = "basic123")
    private String basicInfoId;
    
    /**
     * 转诊编号
     */
    @Schema(description = "转诊编号", example = "ZZ20250723001")
    private String referralNo;
    
    /**
     * 转诊日期
     */
    @Schema(description = "转诊日期", example = "2025-07-23T10:30:00")
    private LocalDateTime referralDate;
    
    /**
     * 转诊日期格式化
     */
    @Schema(description = "转诊日期格式化", example = "2025-07-23 10:30")
    private String referralDateFormatted;
    
    // ========== 患者信息 ==========
    
    /**
     * 患者姓名
     */
    @Schema(description = "患者姓名", example = "张三")
    private String patientName;
    
    /**
     * 性别代码
     */
    @Schema(description = "性别代码", example = "1")
    private Integer gender;
    
    /**
     * 性别描述
     */
    @Schema(description = "性别描述", example = "男")
    private String genderDesc;
    
    /**
     * 年龄
     */
    @Schema(description = "年龄", example = "35")
    private Integer age;
    
    /**
     * 年龄描述
     */
    @Schema(description = "年龄描述", example = "35岁")
    private String ageDesc;
    
    /**
     * 身份证号（脱敏）
     */
    @Schema(description = "身份证号（脱敏）", example = "1101****1234")
    private String maskedIdCard;
    
    /**
     * 联系电话（脱敏）
     */
    @Schema(description = "联系电话（脱敏）", example = "138****8000")
    private String maskedPhone;
    
    /**
     * 完整地址
     */
    @Schema(description = "完整地址", example = "北京市朝阳区某某小区1号楼2单元301室")
    private String fullAddress;
    
    /**
     * 档案编号
     */
    @Schema(description = "档案编号", example = "DA20250723001")
    private String fileNumber;
    
    // ========== 医疗信息 ==========
    
    /**
     * 初步诊断
     */
    @Schema(description = "初步诊断", example = "高血压病")
    private String impression;
    
    /**
     * 病史摘要
     */
    @Schema(description = "病史摘要", example = "主要病史: 患者有高血压病史5年")
    private String historySummary;
    
    /**
     * 是否有治疗记录
     */
    @Schema(description = "是否有治疗记录", example = "true")
    private Boolean hasTreatmentRecord;
    
    // ========== 转出医院信息 ==========
    
    /**
     * 转出医院ID
     */
    @Schema(description = "转出医院ID", example = "hospital001")
    private String outUnitId;
    
    /**
     * 转出医院信息
     */
    @Schema(description = "转出医院信息", example = "某某社区卫生服务中心 - 内科")
    private String outHospitalInfo;
    
    /**
     * 转出医生信息
     */
    @Schema(description = "转出医生信息", example = "李医生 (13900139000)")
    private String outDoctorInfo;
    
    // ========== 转入医院信息 ==========
    
    /**
     * 转入医院ID
     */
    @Schema(description = "转入医院ID", example = "hospital002")
    private String inUnitId;
    
    /**
     * 转入医院信息
     */
    @Schema(description = "转入医院信息", example = "某某人民医院 - 心血管内科")
    private String inHospitalInfo;
    
    /**
     * 转入医生信息
     */
    @Schema(description = "转入医生信息", example = "王医生 (13800138001)")
    private String inDoctorInfo;
    
    // ========== 状态信息 ==========
    
    /**
     * 转诊状态代码
     */
    @Schema(description = "转诊状态代码", example = "0")
    private Integer status;
    
    /**
     * 转诊状态描述
     */
    @Schema(description = "转诊状态描述", example = "待确认")
    private String statusDesc;
    
    /**
     * 状态标签样式
     */
    @Schema(description = "状态标签样式", example = "warning")
    private String statusTag;
    
    /**
     * 拒绝原因
     */
    @Schema(description = "拒绝原因", example = "床位不足")
    private String rejectReason;
    
    /**
     * 确认时间
     */
    @Schema(description = "确认时间", example = "2025-07-23T11:00:00")
    private LocalDateTime confirmTime;
    
    /**
     * 确认时间格式化
     */
    @Schema(description = "确认时间格式化", example = "2025-07-23 11:00")
    private String confirmTimeFormatted;
    
    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "2025-07-23T10:30:00")
    private LocalDateTime createTime;
    
    /**
     * 创建时间格式化
     */
    @Schema(description = "创建时间格式化", example = "2025-07-23 10:30")
    private String createTimeFormatted;
    
    // ========== 业务状态 ==========
    
    /**
     * 是否待处理
     */
    @Schema(description = "是否待处理", example = "true")
    private Boolean isPending;
    
    /**
     * 是否已确认
     */
    @Schema(description = "是否已确认", example = "false")
    private Boolean isConfirmed;
    
    /**
     * 是否已完成
     */
    @Schema(description = "是否已完成", example = "false")
    private Boolean isCompleted;
    
    /**
     * 是否已拒绝
     */
    @Schema(description = "是否已拒绝", example = "false")
    private Boolean isRejected;
    
    /**
     * 是否已取消
     */
    @Schema(description = "是否已取消", example = "false")
    private Boolean isCancelled;
    
    /**
     * 是否超时
     */
    @Schema(description = "是否超时", example = "false")
    private Boolean isTimeout;
    
    /**
     * 转诊描述
     */
    @Schema(description = "转诊描述", example = "从 某某社区卫生服务中心 转诊到 某某人民医院")
    private String referralDescription;
    
    // ========== 操作权限 ==========
    
    /**
     * 是否可以确认
     */
    @Schema(description = "是否可以确认", example = "true")
    private Boolean canConfirm;
    
    /**
     * 是否可以拒绝
     */
    @Schema(description = "是否可以拒绝", example = "true")
    private Boolean canReject;
    
    /**
     * 是否可以取消
     */
    @Schema(description = "是否可以取消", example = "true")
    private Boolean canCancel;
    
    /**
     * 是否可以完成
     */
    @Schema(description = "是否可以完成", example = "false")
    private Boolean canComplete;
}
