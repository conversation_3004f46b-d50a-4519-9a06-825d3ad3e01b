package com.hys.hm.shared.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 字段加密注解
 * 用于标记需要加密存储的字段
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-23
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface EncryptField {
    
    /**
     * 是否支持模糊查询
     * 如果为true，会额外生成分词索引用于模糊查询
     */
    boolean fuzzySearch() default false;
    
    /**
     * 分词长度（用于模糊查询）
     * 默认为2，表示按2个字符分词
     */
    int tokenLength() default 2;
    
    /**
     * 加密算法类型
     */
    EncryptType type() default EncryptType.AES;
    
    /**
     * 字段描述（用于日志和配置）
     */
    String description() default "";
    
    /**
     * 加密算法类型枚举
     */
    enum EncryptType {
        AES,    // AES对称加密
        SM4     // 国密SM4算法
    }
}
