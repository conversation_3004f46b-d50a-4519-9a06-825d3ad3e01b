package com.hys.hm.shared.common.service;

import com.hys.hm.shared.common.annotation.EncryptField;

import java.util.List;

/**
 * 加密服务接口
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-23
 */
public interface EncryptService {
    
    /**
     * 加密字符串
     * 
     * @param plainText 明文
     * @param type 加密类型
     * @return 密文
     */
    String encrypt(String plainText, EncryptField.EncryptType type);
    
    /**
     * 解密字符串
     * 
     * @param cipherText 密文
     * @param type 加密类型
     * @return 明文
     */
    String decrypt(String cipherText, EncryptField.EncryptType type);
    
    /**
     * 生成模糊查询的分词哈希
     * 
     * @param plainText 明文
     * @param tokenLength 分词长度
     * @return 分词哈希列表
     */
    List<String> generateFuzzyTokens(String plainText, int tokenLength);
    
    /**
     * 生成精确查询的哈希
     * 
     * @param plainText 明文
     * @return 哈希值
     */
    String generateExactHash(String plainText);
    
    /**
     * 验证明文是否匹配哈希值
     * 
     * @param plainText 明文
     * @param hash 哈希值
     * @return 是否匹配
     */
    boolean verifyHash(String plainText, String hash);
}
