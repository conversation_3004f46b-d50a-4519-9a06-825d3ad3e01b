package com.hys.hm.shared.events;

import java.time.LocalDateTime;

/**
 * 领域事件基础接口
 * 所有领域事件都应该实现此接口
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-29
 */
public interface DomainEvent {
    
    /**
     * 事件ID
     */
    String getEventId();
    
    /**
     * 聚合根ID
     */
    String getAggregateId();
    
    /**
     * 事件时间戳
     */
    LocalDateTime getTimestamp();
    
    /**
     * 事件类型
     */
    String getEventType();
    
    /**
     * 事件版本
     */
    Integer getVersion();
    
    /**
     * 事件来源
     */
    default String getSource() {
        return "health-management-system";
    }
}
