package com.hys.hm.shared.events;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 内部事件发布器
 * 负责发布领域事件到Spring事件总线
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-29
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class EventPublisher {
    
    private final ApplicationEventPublisher applicationEventPublisher;
    
    /**
     * 发布单个领域事件
     * 
     * @param event 领域事件
     */
    public void publishEvent(DomainEvent event) {
        try {
            log.info("发布领域事件: eventType={}, aggregateId={}, eventId={}", 
                    event.getEventType(), event.getAggregateId(), event.getEventId());
            
            applicationEventPublisher.publishEvent(event);
            
            log.debug("领域事件发布成功: eventId={}", event.getEventId());
            
        } catch (Exception e) {
            log.error("发布领域事件失败: eventType={}, aggregateId={}, eventId={}, error={}", 
                    event.getEventType(), event.getAggregateId(), event.getEventId(), e.getMessage(), e);
            throw new EventPublishException("事件发布失败", e);
        }
    }
    
    /**
     * 批量发布领域事件
     * 
     * @param events 领域事件列表
     */
    public void publishEvents(List<DomainEvent> events) {
        if (events == null || events.isEmpty()) {
            return;
        }
        
        log.info("批量发布领域事件: 数量={}", events.size());
        
        for (DomainEvent event : events) {
            publishEvent(event);
        }
        
        log.info("批量发布领域事件完成: 数量={}", events.size());
    }
    
    /**
     * 事件发布异常
     */
    public static class EventPublishException extends RuntimeException {
        public EventPublishException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}
