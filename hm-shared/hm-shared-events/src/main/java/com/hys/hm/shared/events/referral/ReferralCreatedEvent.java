package com.hys.hm.shared.events.referral;

import com.hys.hm.shared.events.DomainEvent;
import com.hys.hm.shared.types.dto.PatientBasicInfoDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * 转诊创建事件
 * 当转诊表单被创建时发布此事件
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-29
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReferralCreatedEvent implements DomainEvent {
    
    /**
     * 事件ID
     */
    @Builder.Default
    private String eventId = UUID.randomUUID().toString();
    
    /**
     * 转诊ID（聚合根ID）
     */
    private String referralId;
    
    /**
     * 转诊编号
     */
    private String referralNo;
    
    /**
     * 患者基础信息
     */
    private PatientBasicInfoDTO patientInfo;
    
    /**
     * 转出医院ID
     */
    private String outUnitId;
    
    /**
     * 转出医院名称
     */
    private String outUnitName;
    
    /**
     * 转入医院ID
     */
    private String inUnitId;
    
    /**
     * 转入医院名称
     */
    private String inUnitName;
    
    /**
     * 转诊原因
     */
    private String referralReason;
    
    /**
     * 紧急程度
     */
    private Integer urgencyLevel;
    
    /**
     * 创建人
     */
    private String createdBy;
    
    /**
     * 事件时间戳
     */
    @Builder.Default
    private LocalDateTime timestamp = LocalDateTime.now();
    
    /**
     * 事件版本
     */
    @Builder.Default
    private Integer version = 1;
    
    @Override
    public String getAggregateId() {
        return referralId;
    }
    
    @Override
    public String getEventType() {
        return "ReferralCreated";
    }
}
