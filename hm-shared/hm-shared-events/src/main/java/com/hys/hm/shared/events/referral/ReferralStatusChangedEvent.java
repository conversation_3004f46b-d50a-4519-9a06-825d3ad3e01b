package com.hys.hm.shared.events.referral;

import com.hys.hm.shared.events.DomainEvent;
import com.hys.hm.shared.types.enums.ReferralStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * 转诊状态变更事件
 * 当转诊状态发生变更时发布此事件
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-29
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReferralStatusChangedEvent implements DomainEvent {
    
    /**
     * 事件ID
     */
    @Builder.Default
    private String eventId = UUID.randomUUID().toString();
    
    /**
     * 转诊ID（聚合根ID）
     */
    private String referralId;
    
    /**
     * 转诊编号
     */
    private String referralNo;
    
    /**
     * 患者ID
     */
    private String patientId;
    
    /**
     * 患者姓名
     */
    private String patientName;
    
    /**
     * 原状态
     */
    private ReferralStatus oldStatus;
    
    /**
     * 新状态
     */
    private ReferralStatus newStatus;
    
    /**
     * 变更原因（如拒绝原因）
     */
    private String reason;
    
    /**
     * 转出医院名称
     */
    private String outUnitName;
    
    /**
     * 转入医院名称
     */
    private String inUnitName;
    
    /**
     * 操作人
     */
    private String operatedBy;
    
    /**
     * 事件时间戳
     */
    @Builder.Default
    private LocalDateTime timestamp = LocalDateTime.now();
    
    /**
     * 事件版本
     */
    @Builder.Default
    private Integer version = 1;
    
    @Override
    public String getAggregateId() {
        return referralId;
    }
    
    @Override
    public String getEventType() {
        return "ReferralStatusChanged";
    }
    
    /**
     * 判断是否为确认事件
     */
    public boolean isConfirmed() {
        return ReferralStatus.CONFIRMED.equals(newStatus);
    }
    
    /**
     * 判断是否为拒绝事件
     */
    public boolean isRejected() {
        return ReferralStatus.REJECTED.equals(newStatus);
    }
    
    /**
     * 判断是否为取消事件
     */
    public boolean isCancelled() {
        return ReferralStatus.CANCELLED.equals(newStatus);
    }
    
    /**
     * 判断是否为完成事件
     */
    public boolean isCompleted() {
        return ReferralStatus.COMPLETED.equals(newStatus);
    }
}
