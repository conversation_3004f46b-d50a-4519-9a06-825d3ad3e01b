package com.hys.hm.shared.framework.config;

import com.hys.hm.shared.framework.query.QueryConditionParser;
import com.hys.hm.shared.framework.repository.BaseRepositoryFactoryBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

/**
 * 框架自动配置类
 * 自动配置框架相关的Bean和设置
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-29
 */
@Slf4j
@Configuration
@EnableJpaAuditing
@EnableConfigurationProperties(FrameworkProperties.class)
@EnableJpaRepositories(
    repositoryFactoryBeanClass = BaseRepositoryFactoryBean.class,
    basePackages = {
        "com.hys.hm.infrastructure.persistence",
        "com.hys.hm.domain.*.repository"
    }
)
public class FrameworkAutoConfiguration {

    /**
     * 查询条件解析器Bean
     */
    @Bean
    @ConditionalOnMissingBean
    public QueryConditionParser queryConditionParser() {
        log.info("初始化查询条件解析器");
        return new QueryConditionParser();
    }

    /**
     * 框架配置属性Bean
     */
    @Bean
    @ConditionalOnMissingBean
    public FrameworkProperties frameworkProperties() {
        log.info("初始化框架配置属性");
        return new FrameworkProperties();
    }
}
