package com.hys.hm.shared.framework.example;

import com.hys.hm.shared.common.Result;
import com.hys.hm.shared.framework.controller.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

/**
 * 示例控制器
 * 演示如何使用框架的基础控制器
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-29
 */
@Slf4j
@RestController
@RequestMapping("/api/examples")
@Tag(name = "示例管理", description = "示例实体的CRUD操作和业务功能")
public class ExampleController extends BaseController<ExampleEntity, String> {

    @Autowired
    private ExampleService exampleService;

    @Override
    protected String getEntityId(ExampleEntity entity) {
        return entity.getId();
    }

    @Override
    protected void setEntityId(ExampleEntity entity, String id) {
        entity.setId(id);
    }

    /**
     * 根据名称查找实体
     */
    @GetMapping("/by-name/{name}")
    @Operation(summary = "根据名称查找实体", description = "根据名称精确查找实体列表")
    public ResponseEntity<Result<List<ExampleEntity>>> findByName(
            @Parameter(description = "名称", required = true) @PathVariable @NotNull String name) {
        try {
            List<ExampleEntity> entities = exampleService.findByName(name);
            return ResponseEntity.ok(Result.success("查询成功", entities));
        } catch (Exception e) {
            log.error("根据名称查找实体失败: name={}, 错误={}", name, e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Result.error("查询失败: " + e.getMessage()));
        }
    }

    /**
     * 根据名称模糊查找实体
     */
    @GetMapping("/search/{keyword}")
    @Operation(summary = "根据名称模糊查找实体", description = "根据关键字模糊查找实体列表")
    public ResponseEntity<Result<List<ExampleEntity>>> searchByName(
            @Parameter(description = "关键字", required = true) @PathVariable @NotNull String keyword) {
        try {
            List<ExampleEntity> entities = exampleService.findByNameLike(keyword);
            return ResponseEntity.ok(Result.success("查询成功", entities));
        } catch (Exception e) {
            log.error("根据名称模糊查找实体失败: keyword={}, 错误={}", keyword, e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Result.error("查询失败: " + e.getMessage()));
        }
    }

    /**
     * 根据分类查找实体
     */
    @GetMapping("/category/{category}")
    @Operation(summary = "根据分类查找实体", description = "根据分类查找实体列表")
    public ResponseEntity<Result<List<ExampleEntity>>> findByCategory(
            @Parameter(description = "分类", required = true) @PathVariable @NotNull String category) {
        try {
            List<ExampleEntity> entities = exampleService.findByCategory(category);
            return ResponseEntity.ok(Result.success("查询成功", entities));
        } catch (Exception e) {
            log.error("根据分类查找实体失败: category={}, 错误={}", category, e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Result.error("查询失败: " + e.getMessage()));
        }
    }

    /**
     * 查找启用的实体
     */
    @GetMapping("/enabled")
    @Operation(summary = "查找启用的实体", description = "查找所有启用状态的实体列表")
    public ResponseEntity<Result<List<ExampleEntity>>> findEnabledEntities() {
        try {
            List<ExampleEntity> entities = exampleService.findEnabledEntities();
            return ResponseEntity.ok(Result.success("查询成功", entities));
        } catch (Exception e) {
            log.error("查找启用的实体失败: 错误={}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Result.error("查询失败: " + e.getMessage()));
        }
    }

    /**
     * 查找公开的实体
     */
    @GetMapping("/public")
    @Operation(summary = "查找公开的实体", description = "查找所有公开的实体列表")
    public ResponseEntity<Result<List<ExampleEntity>>> findPublicEntities() {
        try {
            List<ExampleEntity> entities = exampleService.findPublicEntities();
            return ResponseEntity.ok(Result.success("查询成功", entities));
        } catch (Exception e) {
            log.error("查找公开的实体失败: 错误={}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Result.error("查询失败: " + e.getMessage()));
        }
    }

    /**
     * 启用实体
     */
    @PutMapping("/{id}/enable")
    @Operation(summary = "启用实体", description = "将指定实体设置为启用状态")
    public ResponseEntity<Result<Void>> enableEntity(
            @Parameter(description = "实体ID", required = true) @PathVariable @NotNull String id) {
        try {
            boolean success = exampleService.enableEntity(id);
            if (success) {
                return ResponseEntity.ok(Result.success("启用成功"));
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            log.error("启用实体失败: id={}, 错误={}", id, e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Result.error("启用失败: " + e.getMessage()));
        }
    }

    /**
     * 禁用实体
     */
    @PutMapping("/{id}/disable")
    @Operation(summary = "禁用实体", description = "将指定实体设置为禁用状态")
    public ResponseEntity<Result<Void>> disableEntity(
            @Parameter(description = "实体ID", required = true) @PathVariable @NotNull String id) {
        try {
            boolean success = exampleService.disableEntity(id);
            if (success) {
                return ResponseEntity.ok(Result.success("禁用成功"));
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            log.error("禁用实体失败: id={}, 错误={}", id, e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Result.error("禁用失败: " + e.getMessage()));
        }
    }

    /**
     * 设置实体为公开
     */
    @PutMapping("/{id}/make-public")
    @Operation(summary = "设置实体为公开", description = "将指定实体设置为公开状态")
    public ResponseEntity<Result<Void>> makeEntityPublic(
            @Parameter(description = "实体ID", required = true) @PathVariable @NotNull String id) {
        try {
            boolean success = exampleService.makeEntityPublic(id);
            if (success) {
                return ResponseEntity.ok(Result.success("设置为公开成功"));
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            log.error("设置实体为公开失败: id={}, 错误={}", id, e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Result.error("设置失败: " + e.getMessage()));
        }
    }

    /**
     * 设置实体为私有
     */
    @PutMapping("/{id}/make-private")
    @Operation(summary = "设置实体为私有", description = "将指定实体设置为私有状态")
    public ResponseEntity<Result<Void>> makeEntityPrivate(
            @Parameter(description = "实体ID", required = true) @PathVariable @NotNull String id) {
        try {
            boolean success = exampleService.makeEntityPrivate(id);
            if (success) {
                return ResponseEntity.ok(Result.success("设置为私有成功"));
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            log.error("设置实体为私有失败: id={}, 错误={}", id, e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Result.error("设置失败: " + e.getMessage()));
        }
    }

    /**
     * 更新实体分类
     */
    @PutMapping("/{id}/category")
    @Operation(summary = "更新实体分类", description = "更新指定实体的分类")
    public ResponseEntity<Result<Void>> updateCategory(
            @Parameter(description = "实体ID", required = true) @PathVariable @NotNull String id,
            @Parameter(description = "新分类", required = true) @RequestParam @NotNull String category) {
        try {
            boolean success = exampleService.updateCategory(id, category);
            if (success) {
                return ResponseEntity.ok(Result.success("更新分类成功"));
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            log.error("更新实体分类失败: id={}, category={}, 错误={}", id, category, e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Result.error("更新失败: " + e.getMessage()));
        }
    }

    /**
     * 批量更新分类
     */
    @PutMapping("/batch-update-category")
    @Operation(summary = "批量更新分类", description = "批量将指定分类的实体更新为新分类")
    public ResponseEntity<Result<Integer>> batchUpdateCategory(
            @Parameter(description = "旧分类", required = true) @RequestParam @NotNull String oldCategory,
            @Parameter(description = "新分类", required = true) @RequestParam @NotNull String newCategory) {
        try {
            int updatedCount = exampleService.batchUpdateCategory(oldCategory, newCategory);
            return ResponseEntity.ok(Result.success("批量更新成功", updatedCount));
        } catch (Exception e) {
            log.error("批量更新分类失败: oldCategory={}, newCategory={}, 错误={}", 
                    oldCategory, newCategory, e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Result.error("批量更新失败: " + e.getMessage()));
        }
    }

    /**
     * 根据分类统计数量
     */
    @GetMapping("/count/category/{category}")
    @Operation(summary = "根据分类统计数量", description = "统计指定分类的实体数量")
    public ResponseEntity<Result<Long>> countByCategory(
            @Parameter(description = "分类", required = true) @PathVariable @NotNull String category) {
        try {
            long count = exampleService.countByCategory(category);
            return ResponseEntity.ok(Result.success("统计成功", count));
        } catch (Exception e) {
            log.error("根据分类统计数量失败: category={}, 错误={}", category, e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Result.error("统计失败: " + e.getMessage()));
        }
    }

    /**
     * 统计启用的实体数量
     */
    @GetMapping("/count/enabled")
    @Operation(summary = "统计启用的实体数量", description = "统计所有启用状态的实体数量")
    public ResponseEntity<Result<Long>> countEnabledEntities() {
        try {
            long count = exampleService.countEnabledEntities();
            return ResponseEntity.ok(Result.success("统计成功", count));
        } catch (Exception e) {
            log.error("统计启用的实体数量失败: 错误={}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Result.error("统计失败: " + e.getMessage()));
        }
    }

    /**
     * 复制实体
     */
    @PostMapping("/{id}/copy")
    @Operation(summary = "复制实体", description = "复制指定实体并创建新实体")
    public ResponseEntity<Result<ExampleEntity>> copyEntity(
            @Parameter(description = "实体ID", required = true) @PathVariable @NotNull String id,
            @Parameter(description = "新名称", required = true) @RequestParam @NotNull String newName) {
        try {
            ExampleEntity copiedEntity = exampleService.copyEntity(id, newName);
            return ResponseEntity.ok(Result.success("复制成功", copiedEntity));
        } catch (Exception e) {
            log.error("复制实体失败: id={}, newName={}, 错误={}", id, newName, e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Result.error("复制失败: " + e.getMessage()));
        }
    }

    /**
     * 导出实体数据
     */
    @GetMapping("/export")
    @Operation(summary = "导出实体数据", description = "导出实体数据，可按分类过滤")
    public ResponseEntity<Result<List<ExampleEntity>>> exportEntities(
            @Parameter(description = "分类（可选）") @RequestParam(required = false) String category) {
        try {
            List<ExampleEntity> entities = exampleService.exportEntities(category);
            return ResponseEntity.ok(Result.success("导出成功", entities));
        } catch (Exception e) {
            log.error("导出实体数据失败: category={}, 错误={}", category, e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Result.error("导出失败: " + e.getMessage()));
        }
    }

    /**
     * 导入实体数据
     */
    @PostMapping("/import")
    @Operation(summary = "导入实体数据", description = "批量导入实体数据")
    public ResponseEntity<Result<Integer>> importEntities(
            @Valid @RequestBody @NotEmpty List<ExampleEntity> entities) {
        try {
            int importedCount = exampleService.importEntities(entities);
            return ResponseEntity.ok(Result.success("导入成功", importedCount));
        } catch (Exception e) {
            log.error("导入实体数据失败: 错误={}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Result.error("导入失败: " + e.getMessage()));
        }
    }

    /**
     * 清理过期数据
     */
    @DeleteMapping("/cleanup")
    @Operation(summary = "清理过期数据", description = "清理指定天数之前的过期数据")
    public ResponseEntity<Result<Integer>> cleanupExpiredData(
            @Parameter(description = "保留天数", required = true) @RequestParam @NotNull Integer days) {
        try {
            int cleanedCount = exampleService.cleanupExpiredData(days);
            return ResponseEntity.ok(Result.success("清理成功", cleanedCount));
        } catch (Exception e) {
            log.error("清理过期数据失败: days={}, 错误={}", days, e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Result.error("清理失败: " + e.getMessage()));
        }
    }
}
