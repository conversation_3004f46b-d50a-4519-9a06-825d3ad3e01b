package com.hys.hm.shared.framework.example;

import com.hys.hm.shared.framework.base.BaseEntity;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.Comment;

import java.time.LocalDate;

/**
 * 示例实体类
 * 演示如何使用框架的基础实体类
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-29
 */
@Entity
@Table(name = "example_entity")
@Data
@EqualsAndHashCode(callSuper = true)
@Comment("示例实体表")
public class ExampleEntity extends BaseEntity<String> {

    /**
     * 主键ID
     */
    @Id
    @Column(length = 32)
    @Comment("主键ID")
    private String id;

    /**
     * 名称
     */
    @Column(length = 100, nullable = false)
    @Comment("名称")
    private String name;

    /**
     * 描述
     */
    @Column(length = 500)
    @Comment("描述")
    private String description;

    /**
     * 状态
     * 1-启用，0-禁用
     */
    @Column(nullable = false)
    @Comment("状态：1-启用，0-禁用")
    private Integer status = 1;

    /**
     * 分类
     */
    @Column(length = 50)
    @Comment("分类")
    private String category;

    /**
     * 标签（JSON格式）
     */
    @Column(length = 1000)
    @Comment("标签（JSON格式）")
    private String tags;

    /**
     * 数值字段
     */
    @Column
    @Comment("数值字段")
    private Double value;

    /**
     * 日期字段
     */
    @Column
    @Comment("日期字段")
    private LocalDate date;

    /**
     * 排序号
     */
    @Column
    @Comment("排序号")
    private Integer sortOrder = 0;

    /**
     * 是否公开
     */
    @Column(nullable = false)
    @Comment("是否公开")
    private Boolean isPublic = false;

    @Override
    public String getId() {
        return id;
    }

    @Override
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 判断是否启用
     */
    public boolean isEnabled() {
        return status != null && status == 1;
    }

    /**
     * 启用
     */
    public void enable() {
        this.status = 1;
    }

    /**
     * 禁用
     */
    public void disable() {
        this.status = 0;
    }

    /**
     * 设置为公开
     */
    public void makePublic() {
        this.isPublic = true;
    }

    /**
     * 设置为私有
     */
    public void makePrivate() {
        this.isPublic = false;
    }
}
