package com.hys.hm.shared.framework.example;

import com.hys.hm.shared.framework.repository.BaseRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * 示例仓储接口
 * 演示如何使用框架的基础仓储接口
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-29
 */
@Repository
public interface ExampleRepository extends BaseRepository<ExampleEntity, String> {

    /**
     * 根据名称查找实体
     * 
     * @param name 名称
     * @return 实体列表
     */
    List<ExampleEntity> findByName(String name);

    /**
     * 根据名称模糊查找实体
     * 
     * @param name 名称关键字
     * @return 实体列表
     */
    List<ExampleEntity> findByNameContaining(String name);

    /**
     * 根据状态查找实体
     * 
     * @param status 状态
     * @return 实体列表
     */
    List<ExampleEntity> findByStatus(Integer status);

    /**
     * 根据分类查找实体
     * 
     * @param category 分类
     * @return 实体列表
     */
    List<ExampleEntity> findByCategory(String category);

    /**
     * 根据是否公开查找实体
     * 
     * @param isPublic 是否公开
     * @return 实体列表
     */
    List<ExampleEntity> findByIsPublic(Boolean isPublic);

    /**
     * 根据日期范围查找实体
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 实体列表
     */
    List<ExampleEntity> findByDateBetween(LocalDate startDate, LocalDate endDate);

    /**
     * 根据数值范围查找实体
     * 
     * @param minValue 最小值
     * @param maxValue 最大值
     * @return 实体列表
     */
    List<ExampleEntity> findByValueBetween(Double minValue, Double maxValue);

    /**
     * 查找启用的实体
     * 
     * @return 启用的实体列表
     */
    @Query("SELECT e FROM ExampleEntity e WHERE e.status = 1 AND e.deleted = 0 ORDER BY e.sortOrder ASC, e.createTime DESC")
    List<ExampleEntity> findEnabledEntities();

    /**
     * 查找公开的实体
     * 
     * @return 公开的实体列表
     */
    @Query("SELECT e FROM ExampleEntity e WHERE e.isPublic = true AND e.deleted = 0 ORDER BY e.sortOrder ASC")
    List<ExampleEntity> findPublicEntities();

    /**
     * 根据分类统计数量
     * 
     * @param category 分类
     * @return 数量
     */
    long countByCategory(String category);

    /**
     * 根据状态统计数量
     * 
     * @param status 状态
     * @return 数量
     */
    long countByStatus(Integer status);

    /**
     * 查找指定分类中的第一个实体
     * 
     * @param category 分类
     * @return 实体（可能为空）
     */
    Optional<ExampleEntity> findFirstByCategoryOrderBySortOrderAsc(String category);

    /**
     * 查找最大排序号
     * 
     * @return 最大排序号
     */
    @Query("SELECT COALESCE(MAX(e.sortOrder), 0) FROM ExampleEntity e WHERE e.deleted = 0")
    Integer findMaxSortOrder();

    /**
     * 根据名称和分类查找实体
     * 
     * @param name 名称
     * @param category 分类
     * @return 实体（可能为空）
     */
    Optional<ExampleEntity> findByNameAndCategory(String name, String category);

    /**
     * 查找指定日期之后创建的实体
     * 
     * @param date 日期
     * @return 实体列表
     */
    @Query("SELECT e FROM ExampleEntity e WHERE e.createTime >= :date AND e.deleted = 0 ORDER BY e.createTime DESC")
    List<ExampleEntity> findCreatedAfter(@Param("date") java.time.LocalDateTime date);

    /**
     * 更新实体状态
     * 
     * @param id 实体ID
     * @param status 新状态
     * @return 更新的记录数
     */
    @Query("UPDATE ExampleEntity e SET e.status = :status WHERE e.id = :id")
    int updateStatus(@Param("id") String id, @Param("status") Integer status);

    /**
     * 批量更新分类
     * 
     * @param oldCategory 旧分类
     * @param newCategory 新分类
     * @return 更新的记录数
     */
    @Query("UPDATE ExampleEntity e SET e.category = :newCategory WHERE e.category = :oldCategory AND e.deleted = 0")
    int updateCategory(@Param("oldCategory") String oldCategory, @Param("newCategory") String newCategory);
}
