package com.hys.hm.shared.framework.example;

import com.hys.hm.shared.framework.service.BaseService;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * 示例服务接口
 * 演示如何使用框架的基础服务接口
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-29
 */
public interface ExampleService extends BaseService<ExampleEntity, String> {

    /**
     * 根据名称查找实体
     * 
     * @param name 名称
     * @return 实体列表
     */
    List<ExampleEntity> findByName(String name);

    /**
     * 根据名称模糊查找实体
     * 
     * @param name 名称关键字
     * @return 实体列表
     */
    List<ExampleEntity> findByNameLike(String name);

    /**
     * 根据分类查找实体
     * 
     * @param category 分类
     * @return 实体列表
     */
    List<ExampleEntity> findByCategory(String category);

    /**
     * 查找启用的实体
     * 
     * @return 启用的实体列表
     */
    List<ExampleEntity> findEnabledEntities();

    /**
     * 查找公开的实体
     * 
     * @return 公开的实体列表
     */
    List<ExampleEntity> findPublicEntities();

    /**
     * 启用实体
     * 
     * @param id 实体ID
     * @return 是否成功
     */
    boolean enableEntity(String id);

    /**
     * 禁用实体
     * 
     * @param id 实体ID
     * @return 是否成功
     */
    boolean disableEntity(String id);

    /**
     * 设置实体为公开
     * 
     * @param id 实体ID
     * @return 是否成功
     */
    boolean makeEntityPublic(String id);

    /**
     * 设置实体为私有
     * 
     * @param id 实体ID
     * @return 是否成功
     */
    boolean makeEntityPrivate(String id);

    /**
     * 更新实体分类
     * 
     * @param id 实体ID
     * @param category 新分类
     * @return 是否成功
     */
    boolean updateCategory(String id, String category);

    /**
     * 批量更新分类
     * 
     * @param oldCategory 旧分类
     * @param newCategory 新分类
     * @return 更新的记录数
     */
    int batchUpdateCategory(String oldCategory, String newCategory);

    /**
     * 根据分类统计数量
     * 
     * @param category 分类
     * @return 数量
     */
    long countByCategory(String category);

    /**
     * 统计启用的实体数量
     * 
     * @return 启用的实体数量
     */
    long countEnabledEntities();

    /**
     * 统计公开的实体数量
     * 
     * @return 公开的实体数量
     */
    long countPublicEntities();

    /**
     * 根据日期范围查找实体
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 实体列表
     */
    List<ExampleEntity> findByDateRange(LocalDate startDate, LocalDate endDate);

    /**
     * 根据数值范围查找实体
     * 
     * @param minValue 最小值
     * @param maxValue 最大值
     * @return 实体列表
     */
    List<ExampleEntity> findByValueRange(Double minValue, Double maxValue);

    /**
     * 获取下一个排序号
     * 
     * @return 下一个排序号
     */
    Integer getNextSortOrder();

    /**
     * 调整排序顺序
     * 
     * @param id 实体ID
     * @param newSortOrder 新排序号
     * @return 是否成功
     */
    boolean adjustSortOrder(String id, Integer newSortOrder);

    /**
     * 检查名称是否已存在
     * 
     * @param name 名称
     * @return 是否存在
     */
    boolean isNameExists(String name);

    /**
     * 检查名称在指定分类中是否已存在
     * 
     * @param name 名称
     * @param category 分类
     * @return 是否存在
     */
    boolean isNameExistsInCategory(String name, String category);

    /**
     * 根据名称和分类查找实体
     * 
     * @param name 名称
     * @param category 分类
     * @return 实体（可能为空）
     */
    Optional<ExampleEntity> findByNameAndCategory(String name, String category);

    /**
     * 复制实体
     * 
     * @param id 原实体ID
     * @param newName 新名称
     * @return 复制后的实体
     */
    ExampleEntity copyEntity(String id, String newName);

    /**
     * 导出实体数据
     * 
     * @param category 分类（可选）
     * @return 导出的实体列表
     */
    List<ExampleEntity> exportEntities(String category);

    /**
     * 导入实体数据
     *
     * @param entities 要导入的实体列表
     * @return 导入成功的数量
     */
    int importEntities(List<ExampleEntity> entities);

    /**
     * 清理过期数据
     *
     * @param days 保留天数
     * @return 清理的记录数
     */
    int cleanupExpiredData(int days);
}
