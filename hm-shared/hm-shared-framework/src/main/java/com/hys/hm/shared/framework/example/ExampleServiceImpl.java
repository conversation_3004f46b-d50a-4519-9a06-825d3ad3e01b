package com.hys.hm.shared.framework.example;

import com.hys.hm.shared.framework.service.BaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * 示例服务实现类
 * 演示如何使用框架的基础服务实现类
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-29
 */
@Slf4j
@Service
@Transactional(readOnly = true)
public class ExampleServiceImpl extends BaseServiceImpl<ExampleEntity, String> implements ExampleService {

    @Autowired
    private ExampleRepository exampleRepository;

    @Override
    public List<ExampleEntity> findByName(String name) {
        Assert.hasText(name, "名称不能为空");
        return exampleRepository.findByName(name);
    }

    @Override
    public List<ExampleEntity> findByNameLike(String name) {
        Assert.hasText(name, "名称不能为空");
        return exampleRepository.findByNameContaining(name);
    }

    @Override
    public List<ExampleEntity> findByCategory(String category) {
        Assert.hasText(category, "分类不能为空");
        return exampleRepository.findByCategory(category);
    }

    @Override
    public List<ExampleEntity> findEnabledEntities() {
        return exampleRepository.findEnabledEntities();
    }

    @Override
    public List<ExampleEntity> findPublicEntities() {
        return exampleRepository.findPublicEntities();
    }

    @Override
    @Transactional
    public boolean enableEntity(String id) {
        Assert.hasText(id, "实体ID不能为空");
        
        Optional<ExampleEntity> entityOpt = findById(id);
        if (entityOpt.isPresent()) {
            ExampleEntity entity = entityOpt.get();
            entity.enable();
            save(entity);
            log.info("启用实体成功: ID={}", id);
            return true;
        }
        
        log.warn("启用实体失败，实体不存在: ID={}", id);
        return false;
    }

    @Override
    @Transactional
    public boolean disableEntity(String id) {
        Assert.hasText(id, "实体ID不能为空");
        
        Optional<ExampleEntity> entityOpt = findById(id);
        if (entityOpt.isPresent()) {
            ExampleEntity entity = entityOpt.get();
            entity.disable();
            save(entity);
            log.info("禁用实体成功: ID={}", id);
            return true;
        }
        
        log.warn("禁用实体失败，实体不存在: ID={}", id);
        return false;
    }

    @Override
    @Transactional
    public boolean makeEntityPublic(String id) {
        Assert.hasText(id, "实体ID不能为空");
        
        Optional<ExampleEntity> entityOpt = findById(id);
        if (entityOpt.isPresent()) {
            ExampleEntity entity = entityOpt.get();
            entity.makePublic();
            save(entity);
            log.info("设置实体为公开成功: ID={}", id);
            return true;
        }
        
        log.warn("设置实体为公开失败，实体不存在: ID={}", id);
        return false;
    }

    @Override
    @Transactional
    public boolean makeEntityPrivate(String id) {
        Assert.hasText(id, "实体ID不能为空");
        
        Optional<ExampleEntity> entityOpt = findById(id);
        if (entityOpt.isPresent()) {
            ExampleEntity entity = entityOpt.get();
            entity.makePrivate();
            save(entity);
            log.info("设置实体为私有成功: ID={}", id);
            return true;
        }
        
        log.warn("设置实体为私有失败，实体不存在: ID={}", id);
        return false;
    }

    @Override
    @Transactional
    public boolean updateCategory(String id, String category) {
        Assert.hasText(id, "实体ID不能为空");
        Assert.hasText(category, "分类不能为空");
        
        Optional<ExampleEntity> entityOpt = findById(id);
        if (entityOpt.isPresent()) {
            ExampleEntity entity = entityOpt.get();
            entity.setCategory(category);
            save(entity);
            log.info("更新实体分类成功: ID={}, 分类={}", id, category);
            return true;
        }
        
        log.warn("更新实体分类失败，实体不存在: ID={}", id);
        return false;
    }

    @Override
    @Transactional
    public int batchUpdateCategory(String oldCategory, String newCategory) {
        Assert.hasText(oldCategory, "旧分类不能为空");
        Assert.hasText(newCategory, "新分类不能为空");
        
        int updatedCount = exampleRepository.updateCategory(oldCategory, newCategory);
        log.info("批量更新分类成功: 旧分类={}, 新分类={}, 更新数量={}", oldCategory, newCategory, updatedCount);
        
        return updatedCount;
    }

    @Override
    public long countByCategory(String category) {
        Assert.hasText(category, "分类不能为空");
        return exampleRepository.countByCategory(category);
    }

    @Override
    public long countEnabledEntities() {
        return exampleRepository.countByStatus(1);
    }

    @Override
    public long countPublicEntities() {
        return exampleRepository.countByIsPublic(true);
    }

    @Override
    public List<ExampleEntity> findByDateRange(LocalDate startDate, LocalDate endDate) {
        Assert.notNull(startDate, "开始日期不能为空");
        Assert.notNull(endDate, "结束日期不能为空");
        Assert.isTrue(!startDate.isAfter(endDate), "开始日期不能晚于结束日期");
        
        return exampleRepository.findByDateBetween(startDate, endDate);
    }

    @Override
    public List<ExampleEntity> findByValueRange(Double minValue, Double maxValue) {
        Assert.notNull(minValue, "最小值不能为空");
        Assert.notNull(maxValue, "最大值不能为空");
        Assert.isTrue(minValue <= maxValue, "最小值不能大于最大值");
        
        return exampleRepository.findByValueBetween(minValue, maxValue);
    }

    @Override
    public Integer getNextSortOrder() {
        Integer maxSortOrder = exampleRepository.findMaxSortOrder();
        return maxSortOrder + 1;
    }

    @Override
    @Transactional
    public boolean adjustSortOrder(String id, Integer newSortOrder) {
        Assert.hasText(id, "实体ID不能为空");
        Assert.notNull(newSortOrder, "排序号不能为空");
        Assert.isTrue(newSortOrder >= 0, "排序号不能为负数");
        
        Optional<ExampleEntity> entityOpt = findById(id);
        if (entityOpt.isPresent()) {
            ExampleEntity entity = entityOpt.get();
            entity.setSortOrder(newSortOrder);
            save(entity);
            log.info("调整排序顺序成功: ID={}, 排序号={}", id, newSortOrder);
            return true;
        }
        
        log.warn("调整排序顺序失败，实体不存在: ID={}", id);
        return false;
    }

    @Override
    public boolean isNameExists(String name) {
        Assert.hasText(name, "名称不能为空");
        return !exampleRepository.findByName(name).isEmpty();
    }

    @Override
    public boolean isNameExistsInCategory(String name, String category) {
        Assert.hasText(name, "名称不能为空");
        Assert.hasText(category, "分类不能为空");
        return exampleRepository.findByNameAndCategory(name, category).isPresent();
    }

    @Override
    public Optional<ExampleEntity> findByNameAndCategory(String name, String category) {
        Assert.hasText(name, "名称不能为空");
        Assert.hasText(category, "分类不能为空");
        return exampleRepository.findByNameAndCategory(name, category);
    }

    @Override
    @Transactional
    public ExampleEntity copyEntity(String id, String newName) {
        Assert.hasText(id, "实体ID不能为空");
        Assert.hasText(newName, "新名称不能为空");
        
        ExampleEntity originalEntity = getById(id);
        
        ExampleEntity copiedEntity = new ExampleEntity();
        copiedEntity.setId(UUID.randomUUID().toString().replace("-", ""));
        copiedEntity.setName(newName);
        copiedEntity.setDescription(originalEntity.getDescription());
        copiedEntity.setStatus(originalEntity.getStatus());
        copiedEntity.setCategory(originalEntity.getCategory());
        copiedEntity.setTags(originalEntity.getTags());
        copiedEntity.setValue(originalEntity.getValue());
        copiedEntity.setDate(originalEntity.getDate());
        copiedEntity.setSortOrder(getNextSortOrder());
        copiedEntity.setIsPublic(originalEntity.getIsPublic());
        
        ExampleEntity savedEntity = save(copiedEntity);
        log.info("复制实体成功: 原ID={}, 新ID={}, 新名称={}", id, savedEntity.getId(), newName);
        
        return savedEntity;
    }

    @Override
    public List<ExampleEntity> exportEntities(String category) {
        if (StringUtils.hasText(category)) {
            return findByCategory(category);
        } else {
            return findAllNotDeleted();
        }
    }

    @Override
    @Transactional
    public int importEntities(List<ExampleEntity> entities) {
        Assert.notEmpty(entities, "实体列表不能为空");
        
        int importedCount = 0;
        for (ExampleEntity entity : entities) {
            try {
                // 生成新的ID
                entity.setId(UUID.randomUUID().toString().replace("-", ""));
                
                // 检查名称是否重复
                if (isNameExists(entity.getName())) {
                    entity.setName(entity.getName() + "_" + System.currentTimeMillis());
                }
                
                save(entity);
                importedCount++;
            } catch (Exception e) {
                log.warn("导入实体失败: 名称={}, 错误={}", entity.getName(), e.getMessage());
            }
        }
        
        log.info("导入实体完成: 总数={}, 成功={}", entities.size(), importedCount);
        return importedCount;
    }

    @Override
    @Transactional
    public int cleanupExpiredData(int days) {
        Assert.isTrue(days > 0, "保留天数必须大于0");
        
        LocalDateTime cutoffTime = LocalDateTime.now().minusDays(days);
        List<ExampleEntity> expiredEntities = exampleRepository.findCreatedAfter(cutoffTime);
        
        int cleanedCount = 0;
        for (ExampleEntity entity : expiredEntities) {
            if (softDeleteById(entity.getId())) {
                cleanedCount++;
            }
        }
        
        log.info("清理过期数据完成: 保留天数={}, 清理数量={}", days, cleanedCount);
        return cleanedCount;
    }

    // 业务逻辑钩子方法的重写

    @Override
    public void validate(ExampleEntity entity) {
        Assert.notNull(entity, "实体不能为空");
        Assert.hasText(entity.getName(), "名称不能为空");
        Assert.notNull(entity.getStatus(), "状态不能为空");
        
        // 检查名称长度
        if (entity.getName().length() > 100) {
            throw new IllegalArgumentException("名称长度不能超过100个字符");
        }
        
        // 检查状态值
        if (entity.getStatus() != 0 && entity.getStatus() != 1) {
            throw new IllegalArgumentException("状态值只能为0或1");
        }
        
        // 检查排序号
        if (entity.getSortOrder() != null && entity.getSortOrder() < 0) {
            throw new IllegalArgumentException("排序号不能为负数");
        }
    }

    @Override
    public void beforeSave(ExampleEntity entity) {
        // 如果没有设置ID，自动生成
        if (!StringUtils.hasText(entity.getId())) {
            entity.setId(UUID.randomUUID().toString().replace("-", ""));
        }
        
        // 如果没有设置排序号，自动设置
        if (entity.getSortOrder() == null) {
            entity.setSortOrder(getNextSortOrder());
        }
        
        // 如果没有设置是否公开，默认为私有
        if (entity.getIsPublic() == null) {
            entity.setIsPublic(false);
        }
        
        log.debug("保存前处理完成: ID={}, 名称={}", entity.getId(), entity.getName());
    }

    @Override
    public void afterSave(ExampleEntity entity) {
        log.info("实体保存成功: ID={}, 名称={}", entity.getId(), entity.getName());
        
        // 可以在这里添加保存后的业务逻辑，如发送通知、更新缓存等
        refreshCache();
    }

    @Override
    public void beforeUpdate(ExampleEntity entity) {
        log.debug("更新前处理: ID={}, 名称={}", entity.getId(), entity.getName());
        
        // 可以在这里添加更新前的业务逻辑
    }

    @Override
    public void afterUpdate(ExampleEntity entity) {
        log.info("实体更新成功: ID={}, 名称={}", entity.getId(), entity.getName());
        
        // 可以在这里添加更新后的业务逻辑
        refreshCache();
    }

    @Override
    public void beforeDelete(String id) {
        log.info("准备删除实体: ID={}", id);
        
        // 可以在这里添加删除前的业务逻辑，如检查关联数据
    }

    @Override
    public void afterDelete(String id) {
        log.info("实体删除成功: ID={}", id);
        
        // 可以在这里添加删除后的业务逻辑
        refreshCache();
    }

    @Override
    public void refreshCache() {
        // 刷新缓存的具体实现
        log.debug("刷新缓存");
    }

    @Override
    public void clearCache() {
        // 清空缓存的具体实现
        log.debug("清空缓存");
    }
}
