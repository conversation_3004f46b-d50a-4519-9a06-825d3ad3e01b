package com.hys.hm.shared.framework.page;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.domain.Page;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 分页结果封装类
 * 封装分页查询的结果数据
 * 
 * @param <T> 数据类型
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-29
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PageResult<T> {

    /**
     * 当前页数据列表
     */
    private List<T> content = new ArrayList<>();

    /**
     * 当前页码（从1开始）
     */
    private int page;

    /**
     * 每页大小
     */
    private int size;

    /**
     * 总记录数
     */
    private long total;

    /**
     * 总页数
     */
    private int totalPages;

    /**
     * 是否为第一页
     */
    private boolean first;

    /**
     * 是否为最后一页
     */
    private boolean last;

    /**
     * 是否有下一页
     */
    private boolean hasNext;

    /**
     * 是否有上一页
     */
    private boolean hasPrevious;

    /**
     * 当前页实际记录数
     */
    private int numberOfElements;

    /**
     * 是否为空页
     */
    private boolean empty;

    /**
     * 创建空的分页结果
     */
    public static <T> PageResult<T> empty() {
        return empty(1, 20);
    }

    /**
     * 创建空的分页结果
     */
    public static <T> PageResult<T> empty(int page, int size) {
        PageResult<T> result = new PageResult<>();
        result.content = new ArrayList<>();
        result.page = page;
        result.size = size;
        result.total = 0;
        result.totalPages = 0;
        result.first = true;
        result.last = true;
        result.hasNext = false;
        result.hasPrevious = false;
        result.numberOfElements = 0;
        result.empty = true;
        return result;
    }

    /**
     * 从Spring Data的Page对象创建分页结果
     */
    public static <T> PageResult<T> of(Page<T> page) {
        PageResult<T> result = new PageResult<>();
        result.content = page.getContent();
        result.page = page.getNumber() + 1; // Spring Data页码从0开始，转换为从1开始
        result.size = page.getSize();
        result.total = page.getTotalElements();
        result.totalPages = page.getTotalPages();
        result.first = page.isFirst();
        result.last = page.isLast();
        result.hasNext = page.hasNext();
        result.hasPrevious = page.hasPrevious();
        result.numberOfElements = page.getNumberOfElements();
        result.empty = page.isEmpty();
        return result;
    }

    /**
     * 创建分页结果
     */
    public static <T> PageResult<T> of(List<T> content, int page, int size, long total) {
        PageResult<T> result = new PageResult<>();
        result.content = content != null ? content : new ArrayList<>();
        result.page = page;
        result.size = size;
        result.total = total;
        result.totalPages = size > 0 ? (int) Math.ceil((double) total / size) : 0;
        result.first = page <= 1;
        result.last = page >= result.totalPages;
        result.hasNext = page < result.totalPages;
        result.hasPrevious = page > 1;
        result.numberOfElements = result.content.size();
        result.empty = result.content.isEmpty();
        return result;
    }

    /**
     * 创建不需要总数的分页结果（性能优化）
     */
    public static <T> PageResult<T> ofWithoutTotal(List<T> content, int page, int size) {
        PageResult<T> result = new PageResult<>();
        result.content = content != null ? content : new ArrayList<>();
        result.page = page;
        result.size = size;
        result.total = -1; // -1表示未计算总数
        result.totalPages = -1;
        result.first = page <= 1;
        result.last = content == null || content.size() < size; // 如果返回数据少于页面大小，认为是最后一页
        result.hasNext = !result.last;
        result.hasPrevious = page > 1;
        result.numberOfElements = result.content.size();
        result.empty = result.content.isEmpty();
        return result;
    }

    /**
     * 转换分页结果的数据类型
     */
    public <R> PageResult<R> map(Function<T, R> converter) {
        List<R> convertedContent = this.content.stream()
                .map(converter)
                .collect(Collectors.toList());
        
        PageResult<R> result = new PageResult<>();
        result.content = convertedContent;
        result.page = this.page;
        result.size = this.size;
        result.total = this.total;
        result.totalPages = this.totalPages;
        result.first = this.first;
        result.last = this.last;
        result.hasNext = this.hasNext;
        result.hasPrevious = this.hasPrevious;
        result.numberOfElements = convertedContent.size();
        result.empty = convertedContent.isEmpty();
        return result;
    }

    /**
     * 获取起始记录号（从1开始）
     */
    public long getStartRow() {
        return empty ? 0 : (long) (page - 1) * size + 1;
    }

    /**
     * 获取结束记录号
     */
    public long getEndRow() {
        return empty ? 0 : getStartRow() + numberOfElements - 1;
    }

    /**
     * 获取偏移量
     */
    public long getOffset() {
        return (long) (page - 1) * size;
    }

    /**
     * 是否有总数信息
     */
    public boolean hasTotalInfo() {
        return total >= 0;
    }

    /**
     * 获取分页信息摘要
     */
    public String getSummary() {
        if (empty) {
            return "无数据";
        }
        
        if (hasTotalInfo()) {
            return String.format("第 %d-%d 条，共 %d 条记录，第 %d/%d 页", 
                    getStartRow(), getEndRow(), total, page, totalPages);
        } else {
            return String.format("第 %d 页，每页 %d 条，当前 %d 条记录", 
                    page, size, numberOfElements);
        }
    }

    /**
     * 创建下一页的分页请求参数
     */
    public PageRequest nextPageRequest() {
        return PageRequest.of(hasNext ? page + 1 : page, size);
    }

    /**
     * 创建上一页的分页请求参数
     */
    public PageRequest previousPageRequest() {
        return PageRequest.of(hasPrevious ? page - 1 : page, size);
    }

    /**
     * 创建第一页的分页请求参数
     */
    public PageRequest firstPageRequest() {
        return PageRequest.of(1, size);
    }

    /**
     * 创建最后一页的分页请求参数
     */
    public PageRequest lastPageRequest() {
        return PageRequest.of(hasTotalInfo() ? totalPages : page, size);
    }

    /**
     * 合并多个分页结果（用于聚合查询）
     */
    public static <T> PageResult<T> merge(List<PageResult<T>> pageResults) {
        if (pageResults == null || pageResults.isEmpty()) {
            return empty();
        }
        
        if (pageResults.size() == 1) {
            return pageResults.get(0);
        }
        
        // 取第一个结果的分页信息
        PageResult<T> first = pageResults.get(0);
        List<T> mergedContent = new ArrayList<>();
        long totalCount = 0;
        
        for (PageResult<T> pageResult : pageResults) {
            if (pageResult.content != null) {
                mergedContent.addAll(pageResult.content);
            }
            if (pageResult.hasTotalInfo()) {
                totalCount += pageResult.total;
            }
        }
        
        return of(mergedContent, first.page, first.size, totalCount);
    }

    @Override
    public String toString() {
        return String.format("PageResult{page=%d, size=%d, total=%d, elements=%d, empty=%s}", 
                page, size, total, numberOfElements, empty);
    }
}
