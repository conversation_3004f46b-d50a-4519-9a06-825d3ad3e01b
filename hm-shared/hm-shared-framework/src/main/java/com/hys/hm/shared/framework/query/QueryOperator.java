package com.hys.hm.shared.framework.query;

/**
 * 查询操作符枚举
 * 定义支持的查询操作类型
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-29
 */
public enum QueryOperator {

    /**
     * 等于
     */
    EQ("=", "等于"),

    /**
     * 不等于
     */
    NE("!=", "不等于"),

    /**
     * 模糊查询
     */
    LIKE("LIKE", "模糊查询"),

    /**
     * 大于
     */
    GT(">", "大于"),

    /**
     * 大于等于
     */
    GTE(">=", "大于等于"),

    /**
     * 小于
     */
    LT("<", "小于"),

    /**
     * 小于等于
     */
    LTE("<=", "小于等于"),

    /**
     * 在列表中
     */
    IN("IN", "在列表中"),

    /**
     * 不在列表中
     */
    NOT_IN("NOT IN", "不在列表中"),

    /**
     * 为空
     */
    IS_NULL("IS NULL", "为空"),

    /**
     * 不为空
     */
    IS_NOT_NULL("IS NOT NULL", "不为空"),

    /**
     * 区间查询
     */
    BETWEEN("BETWEEN", "区间查询"),

    /**
     * 不在区间内
     */
    NOT_BETWEEN("NOT BETWEEN", "不在区间内");

    /**
     * 操作符符号
     */
    private final String symbol;

    /**
     * 操作符描述
     */
    private final String description;

    QueryOperator(String symbol, String description) {
        this.symbol = symbol;
        this.description = description;
    }

    public String getSymbol() {
        return symbol;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据字符串获取操作符
     * 支持以下格式：
     * - fieldName_LIKE
     * - fieldName_EQ 或 fieldName
     * - fieldName_GT
     * - fieldName_LT
     * - fieldName_GTE
     * - fieldName_LTE
     * - fieldName_IN
     * - fieldName_NOT_NULL
     * - fieldName_IS_NULL
     */
    public static QueryOperator fromString(String operatorStr) {
        if (operatorStr == null || operatorStr.trim().isEmpty()) {
            return EQ; // 默认为等于
        }

        String upperStr = operatorStr.toUpperCase();
        
        switch (upperStr) {
            case "LIKE":
                return LIKE;
            case "EQ":
            case "EQUAL":
            case "EQUALS":
                return EQ;
            case "NE":
            case "NOT_EQUAL":
            case "NOT_EQUALS":
                return NE;
            case "GT":
            case "GREATER_THAN":
                return GT;
            case "GTE":
            case "GREATER_THAN_EQUAL":
                return GTE;
            case "LT":
            case "LESS_THAN":
                return LT;
            case "LTE":
            case "LESS_THAN_EQUAL":
                return LTE;
            case "IN":
                return IN;
            case "NOT_IN":
                return NOT_IN;
            case "IS_NULL":
            case "NULL":
                return IS_NULL;
            case "IS_NOT_NULL":
            case "NOT_NULL":
                return IS_NOT_NULL;
            case "BETWEEN":
                return BETWEEN;
            case "NOT_BETWEEN":
                return NOT_BETWEEN;
            default:
                return EQ; // 默认为等于
        }
    }

    /**
     * 判断操作符是否需要值
     */
    public boolean requiresValue() {
        return this != IS_NULL && this != IS_NOT_NULL;
    }

    /**
     * 判断操作符是否需要多个值
     */
    public boolean requiresMultipleValues() {
        return this == IN || this == NOT_IN || this == BETWEEN || this == NOT_BETWEEN;
    }

    /**
     * 判断操作符是否支持字符串操作
     */
    public boolean supportsStringOperation() {
        return this == EQ || this == NE || this == LIKE || this == IN || this == NOT_IN;
    }

    /**
     * 判断操作符是否支持数值比较
     */
    public boolean supportsNumericComparison() {
        return this == EQ || this == NE || this == GT || this == GTE || 
               this == LT || this == LTE || this == BETWEEN || this == NOT_BETWEEN;
    }

    /**
     * 判断操作符是否支持日期比较
     */
    public boolean supportsDateComparison() {
        return supportsNumericComparison();
    }
}
