package com.hys.hm.shared.framework.service;

import com.hys.hm.shared.framework.page.PageRequest;
import com.hys.hm.shared.framework.page.PageResult;
import com.hys.hm.shared.framework.query.QueryCondition;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 基础服务接口
 * 提供通用的业务逻辑操作
 * 
 * @param <T> 实体类型
 * @param <ID> 主键类型
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-29
 */
public interface BaseService<T, ID extends Serializable> {

    /**
     * 保存实体
     * 
     * @param entity 实体对象
     * @return 保存后的实体
     */
    T save(T entity);

    /**
     * 批量保存实体
     * 
     * @param entities 实体列表
     * @return 保存后的实体列表
     */
    List<T> saveAll(List<T> entities);

    /**
     * 更新实体
     * 
     * @param entity 实体对象
     * @return 更新后的实体
     */
    T update(T entity);

    /**
     * 批量更新实体
     * 
     * @param entities 实体列表
     * @return 更新后的实体列表
     */
    List<T> updateAll(List<T> entities);

    /**
     * 保存或更新实体
     * 
     * @param entity 实体对象
     * @return 保存或更新后的实体
     */
    T saveOrUpdate(T entity);

    /**
     * 根据ID删除实体
     * 
     * @param id 实体ID
     * @return 是否删除成功
     */
    boolean deleteById(ID id);

    /**
     * 批量删除实体
     * 
     * @param ids 实体ID列表
     * @return 删除成功的数量
     */
    int deleteByIds(List<ID> ids);

    /**
     * 软删除实体
     * 
     * @param id 实体ID
     * @return 是否删除成功
     */
    boolean softDeleteById(ID id);

    /**
     * 批量软删除实体
     * 
     * @param ids 实体ID列表
     * @return 删除成功的数量
     */
    int softDeleteByIds(List<ID> ids);

    /**
     * 恢复软删除的实体
     * 
     * @param id 实体ID
     * @return 是否恢复成功
     */
    boolean restoreById(ID id);

    /**
     * 根据ID查找实体
     * 
     * @param id 实体ID
     * @return 实体（可能为空）
     */
    Optional<T> findById(ID id);

    /**
     * 根据ID查找实体（必须存在）
     * 
     * @param id 实体ID
     * @return 实体
     * @throws IllegalArgumentException 如果实体不存在
     */
    T getById(ID id);

    /**
     * 根据ID列表查找实体列表
     * 
     * @param ids 实体ID列表
     * @return 实体列表
     */
    List<T> findByIds(List<ID> ids);

    /**
     * 查找所有实体
     * 
     * @return 实体列表
     */
    List<T> findAll();

    /**
     * 分页查找所有实体
     * 
     * @param pageRequest 分页请求
     * @return 分页结果
     */
    PageResult<T> findAll(PageRequest pageRequest);

    /**
     * 根据查询条件查找实体列表
     * 
     * @param conditions 查询条件列表
     * @return 实体列表
     */
    List<T> findByConditions(List<QueryCondition> conditions);

    /**
     * 根据分页请求查找实体列表
     * 
     * @param pageRequest 分页请求（包含查询条件和排序）
     * @return 分页结果
     */
    PageResult<T> findByPageRequest(PageRequest pageRequest);

    /**
     * 根据字段值查找实体列表
     * 
     * @param fieldName 字段名
     * @param value 字段值
     * @return 实体列表
     */
    List<T> findByField(String fieldName, Object value);

    /**
     * 根据字段值查找第一个实体
     * 
     * @param fieldName 字段名
     * @param value 字段值
     * @return 实体（可能为空）
     */
    Optional<T> findFirstByField(String fieldName, Object value);

    /**
     * 根据字段值模糊查找实体列表
     * 
     * @param fieldName 字段名
     * @param value 字段值
     * @return 实体列表
     */
    List<T> findByFieldLike(String fieldName, Object value);

    /**
     * 根据多个字段值查找实体列表
     * 
     * @param fieldValues 字段名-值映射
     * @return 实体列表
     */
    List<T> findByFields(Map<String, Object> fieldValues);

    /**
     * 根据查询条件统计数量
     * 
     * @param conditions 查询条件列表
     * @return 记录数量
     */
    long countByConditions(List<QueryCondition> conditions);

    /**
     * 统计所有记录数量
     * 
     * @return 记录数量
     */
    long count();

    /**
     * 统计未删除的记录数量
     * 
     * @return 未删除的记录数量
     */
    long countNotDeleted();

    /**
     * 根据查询条件检查是否存在
     * 
     * @param conditions 查询条件列表
     * @return 是否存在
     */
    boolean existsByConditions(List<QueryCondition> conditions);

    /**
     * 根据ID检查是否存在
     * 
     * @param id 实体ID
     * @return 是否存在
     */
    boolean existsById(ID id);

    /**
     * 根据字段值检查是否存在
     * 
     * @param fieldName 字段名
     * @param value 字段值
     * @return 是否存在
     */
    boolean existsByField(String fieldName, Object value);

    /**
     * 查找未删除的实体列表
     * 
     * @return 未删除的实体列表
     */
    List<T> findAllNotDeleted();

    /**
     * 分页查找未删除的实体列表
     * 
     * @param pageRequest 分页请求
     * @return 分页结果
     */
    PageResult<T> findAllNotDeleted(PageRequest pageRequest);

    /**
     * 根据ID查找未删除的实体
     * 
     * @param id 实体ID
     * @return 实体（可能为空）
     */
    Optional<T> findByIdNotDeleted(ID id);

    /**
     * 根据条件更新字段值
     * 
     * @param conditions 查询条件
     * @param fieldName 要更新的字段名
     * @param newValue 新值
     * @return 更新的记录数
     */
    int updateFieldByConditions(List<QueryCondition> conditions, String fieldName, Object newValue);

    /**
     * 根据条件批量更新多个字段
     * 
     * @param conditions 查询条件
     * @param fieldValues 字段名-值映射
     * @return 更新的记录数
     */
    int updateFieldsByConditions(List<QueryCondition> conditions, Map<String, Object> fieldValues);

    /**
     * 验证实体数据
     * 
     * @param entity 实体对象
     * @throws IllegalArgumentException 如果验证失败
     */
    void validate(T entity);

    /**
     * 在保存前执行的业务逻辑
     * 
     * @param entity 实体对象
     */
    void beforeSave(T entity);

    /**
     * 在保存后执行的业务逻辑
     * 
     * @param entity 实体对象
     */
    void afterSave(T entity);

    /**
     * 在更新前执行的业务逻辑
     * 
     * @param entity 实体对象
     */
    void beforeUpdate(T entity);

    /**
     * 在更新后执行的业务逻辑
     * 
     * @param entity 实体对象
     */
    void afterUpdate(T entity);

    /**
     * 在删除前执行的业务逻辑
     * 
     * @param id 实体ID
     */
    void beforeDelete(ID id);

    /**
     * 在删除后执行的业务逻辑
     * 
     * @param id 实体ID
     */
    void afterDelete(ID id);

    /**
     * 刷新缓存
     */
    void refreshCache();

    /**
     * 清空缓存
     */
    void clearCache();

    /**
     * 获取实体类型
     *
     * @return 实体类型
     */
    Class<T> getEntityClass();

    /**
     * 获取实体名称
     *
     * @return 实体名称
     */
    String getEntityName();
}
