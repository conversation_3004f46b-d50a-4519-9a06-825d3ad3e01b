package com.hys.hm.shared.framework.service;

import com.hys.hm.shared.framework.base.BaseEntity;
import com.hys.hm.shared.framework.page.PageRequest;
import com.hys.hm.shared.framework.page.PageResult;
import com.hys.hm.shared.framework.query.QueryCondition;
import com.hys.hm.shared.framework.repository.BaseRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.io.Serializable;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 基础服务实现类
 * 提供通用的业务逻辑操作的具体实现
 * 
 * @param <T> 实体类型
 * @param <ID> 主键类型
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-29
 */
@Slf4j
@Transactional(readOnly = true)
public abstract class BaseServiceImpl<T, ID extends Serializable> implements BaseService<T, ID> {

    @Autowired
    protected BaseRepository<T, ID> baseRepository;

    private Class<T> entityClass;

    @SuppressWarnings("unchecked")
    public BaseServiceImpl() {
        // 通过反射获取实体类型
        Type superClass = getClass().getGenericSuperclass();
        if (superClass instanceof ParameterizedType) {
            ParameterizedType parameterizedType = (ParameterizedType) superClass;
            Type[] typeArguments = parameterizedType.getActualTypeArguments();
            if (typeArguments.length > 0) {
                this.entityClass = (Class<T>) typeArguments[0];
            }
        }
    }

    @Override
    @Transactional
    public T save(T entity) {
        Assert.notNull(entity, "实体不能为空");
        
        validate(entity);
        beforeSave(entity);
        
        T savedEntity = baseRepository.save(entity);
        
        afterSave(savedEntity);
        log.debug("保存实体成功: {}", savedEntity);
        
        return savedEntity;
    }

    @Override
    @Transactional
    public List<T> saveAll(List<T> entities) {
        Assert.notEmpty(entities, "实体列表不能为空");
        
        for (T entity : entities) {
            validate(entity);
            beforeSave(entity);
        }
        
        List<T> savedEntities = baseRepository.saveAll(entities);
        
        for (T entity : savedEntities) {
            afterSave(entity);
        }
        
        log.debug("批量保存实体成功: {} 条", savedEntities.size());
        return savedEntities;
    }

    @Override
    @Transactional
    public T update(T entity) {
        Assert.notNull(entity, "实体不能为空");
        
        validate(entity);
        beforeUpdate(entity);
        
        T updatedEntity = baseRepository.save(entity);
        
        afterUpdate(updatedEntity);
        log.debug("更新实体成功: {}", updatedEntity);
        
        return updatedEntity;
    }

    @Override
    @Transactional
    public List<T> updateAll(List<T> entities) {
        Assert.notEmpty(entities, "实体列表不能为空");
        
        for (T entity : entities) {
            validate(entity);
            beforeUpdate(entity);
        }
        
        List<T> updatedEntities = baseRepository.updateAll(entities);
        
        for (T entity : updatedEntities) {
            afterUpdate(entity);
        }
        
        log.debug("批量更新实体成功: {} 条", updatedEntities.size());
        return updatedEntities;
    }

    @Override
    @Transactional
    public T saveOrUpdate(T entity) {
        Assert.notNull(entity, "实体不能为空");
        
        if (entity instanceof BaseEntity) {
            BaseEntity<?> baseEntity = (BaseEntity<?>) entity;
            if (baseEntity.isNew()) {
                return save(entity);
            } else {
                return update(entity);
            }
        } else {
            // 对于非BaseEntity，直接使用save方法（JPA会自动判断是插入还是更新）
            return save(entity);
        }
    }

    @Override
    @Transactional
    public boolean deleteById(ID id) {
        Assert.notNull(id, "实体ID不能为空");
        
        if (!baseRepository.existsById(id)) {
            log.warn("要删除的实体不存在: ID={}", id);
            return false;
        }
        
        beforeDelete(id);
        baseRepository.deleteById(id);
        afterDelete(id);
        
        log.debug("删除实体成功: ID={}", id);
        return true;
    }

    @Override
    @Transactional
    public int deleteByIds(List<ID> ids) {
        Assert.notEmpty(ids, "实体ID列表不能为空");
        
        int deletedCount = 0;
        for (ID id : ids) {
            if (deleteById(id)) {
                deletedCount++;
            }
        }
        
        log.debug("批量删除实体成功: {} 条", deletedCount);
        return deletedCount;
    }

    @Override
    @Transactional
    public boolean softDeleteById(ID id) {
        Assert.notNull(id, "实体ID不能为空");
        
        beforeDelete(id);
        boolean result = baseRepository.softDeleteById(id);
        
        if (result) {
            afterDelete(id);
            log.debug("软删除实体成功: ID={}", id);
        } else {
            log.warn("软删除实体失败: ID={}", id);
        }
        
        return result;
    }

    @Override
    @Transactional
    public int softDeleteByIds(List<ID> ids) {
        Assert.notEmpty(ids, "实体ID列表不能为空");
        
        int deletedCount = baseRepository.softDeleteByIds(ids);
        log.debug("批量软删除实体成功: {} 条", deletedCount);
        
        return deletedCount;
    }

    @Override
    @Transactional
    public boolean restoreById(ID id) {
        Assert.notNull(id, "实体ID不能为空");
        
        boolean result = baseRepository.restoreById(id);
        
        if (result) {
            log.debug("恢复软删除实体成功: ID={}", id);
        } else {
            log.warn("恢复软删除实体失败: ID={}", id);
        }
        
        return result;
    }

    @Override
    public Optional<T> findById(ID id) {
        Assert.notNull(id, "实体ID不能为空");
        return baseRepository.findById(id);
    }

    @Override
    public T getById(ID id) {
        return findById(id).orElseThrow(() -> 
                new IllegalArgumentException("实体不存在: ID=" + id));
    }

    @Override
    public List<T> findByIds(List<ID> ids) {
        Assert.notEmpty(ids, "实体ID列表不能为空");
        return baseRepository.findAllById(ids);
    }

    @Override
    public List<T> findAll() {
        return baseRepository.findAll();
    }

    @Override
    public PageResult<T> findAll(PageRequest pageRequest) {
        Assert.notNull(pageRequest, "分页请求不能为空");
        return baseRepository.findByPageRequest(pageRequest);
    }

    @Override
    public List<T> findByConditions(List<QueryCondition> conditions) {
        return baseRepository.findByConditions(conditions);
    }

    @Override
    public PageResult<T> findByPageRequest(PageRequest pageRequest) {
        Assert.notNull(pageRequest, "分页请求不能为空");
        return baseRepository.findByPageRequest(pageRequest);
    }

    @Override
    public List<T> findByField(String fieldName, Object value) {
        Assert.hasText(fieldName, "字段名不能为空");
        return baseRepository.findByField(fieldName, value);
    }

    @Override
    public Optional<T> findFirstByField(String fieldName, Object value) {
        Assert.hasText(fieldName, "字段名不能为空");
        return baseRepository.findFirstByField(fieldName, value);
    }

    @Override
    public List<T> findByFieldLike(String fieldName, Object value) {
        Assert.hasText(fieldName, "字段名不能为空");
        return baseRepository.findByFieldLike(fieldName, value);
    }

    @Override
    public List<T> findByFields(Map<String, Object> fieldValues) {
        Assert.notEmpty(fieldValues, "字段值映射不能为空");
        return baseRepository.findByFields(fieldValues);
    }

    @Override
    public long countByConditions(List<QueryCondition> conditions) {
        return baseRepository.countByConditions(conditions);
    }

    @Override
    public long count() {
        return baseRepository.count();
    }

    @Override
    public long countNotDeleted() {
        return baseRepository.countNotDeleted();
    }

    @Override
    public boolean existsByConditions(List<QueryCondition> conditions) {
        return baseRepository.existsByConditions(conditions);
    }

    @Override
    public boolean existsById(ID id) {
        Assert.notNull(id, "实体ID不能为空");
        return baseRepository.existsById(id);
    }

    @Override
    public boolean existsByField(String fieldName, Object value) {
        Assert.hasText(fieldName, "字段名不能为空");
        return !baseRepository.findByField(fieldName, value).isEmpty();
    }

    @Override
    public List<T> findAllNotDeleted() {
        return baseRepository.findAllNotDeleted();
    }

    @Override
    public PageResult<T> findAllNotDeleted(PageRequest pageRequest) {
        Assert.notNull(pageRequest, "分页请求不能为空");
        
        // 添加未删除条件
        pageRequest.addEq("deleted", 0);
        return baseRepository.findByPageRequest(pageRequest);
    }

    @Override
    public Optional<T> findByIdNotDeleted(ID id) {
        Assert.notNull(id, "实体ID不能为空");
        return baseRepository.findByIdNotDeleted(id);
    }

    @Override
    @Transactional
    public int updateFieldByConditions(List<QueryCondition> conditions, String fieldName, Object newValue) {
        Assert.hasText(fieldName, "字段名不能为空");
        return baseRepository.updateFieldByConditions(conditions, fieldName, newValue);
    }

    @Override
    @Transactional
    public int updateFieldsByConditions(List<QueryCondition> conditions, Map<String, Object> fieldValues) {
        Assert.notEmpty(fieldValues, "字段值映射不能为空");
        return baseRepository.updateFieldsByConditions(conditions, fieldValues);
    }

    // 业务逻辑钩子方法的默认实现（子类可以重写）

    @Override
    public void validate(T entity) {
        // 默认不做验证，子类可以重写
    }

    @Override
    public void beforeSave(T entity) {
        // 默认不做处理，子类可以重写
    }

    @Override
    public void afterSave(T entity) {
        // 默认不做处理，子类可以重写
    }

    @Override
    public void beforeUpdate(T entity) {
        // 默认不做处理，子类可以重写
    }

    @Override
    public void afterUpdate(T entity) {
        // 默认不做处理，子类可以重写
    }

    @Override
    public void beforeDelete(ID id) {
        // 默认不做处理，子类可以重写
    }

    @Override
    public void afterDelete(ID id) {
        // 默认不做处理，子类可以重写
    }

    @Override
    public void refreshCache() {
        // 默认不做处理，子类可以重写
    }

    @Override
    public void clearCache() {
        // 默认不做处理，子类可以重写
    }

    @Override
    public Class<T> getEntityClass() {
        return entityClass;
    }

    @Override
    public String getEntityName() {
        return entityClass != null ? entityClass.getSimpleName() : "Unknown";
    }
}
