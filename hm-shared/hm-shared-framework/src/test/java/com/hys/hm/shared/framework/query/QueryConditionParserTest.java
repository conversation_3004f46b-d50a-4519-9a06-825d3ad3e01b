package com.hys.hm.shared.framework.query;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import static org.junit.jupiter.api.Assertions.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 查询条件解析器测试类
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-29
 */
class QueryConditionParserTest {

    private QueryConditionParser parser;

    @BeforeEach
    void setUp() {
        parser = new QueryConditionParser();
    }

    @Test
    void testParseEqualCondition() {
        Map<String, String[]> params = new HashMap<>();
        params.put("name", new String[]{"张三"});
        
        List<QueryCondition> conditions = parser.parseConditions(params);
        
        assertEquals(1, conditions.size());
        QueryCondition condition = conditions.get(0);
        assertEquals("name", condition.getFieldName());
        assertEquals(QueryOperator.EQ, condition.getOperator());
        assertEquals("张三", condition.getValue());
    }

    @Test
    void testParseLikeCondition() {
        Map<String, String[]> params = new HashMap<>();
        params.put("name_LIKE", new String[]{"张"});
        
        List<QueryCondition> conditions = parser.parseConditions(params);
        
        assertEquals(1, conditions.size());
        QueryCondition condition = conditions.get(0);
        assertEquals("name", condition.getFieldName());
        assertEquals(QueryOperator.LIKE, condition.getOperator());
        assertEquals("张", condition.getValue());
        assertEquals("%张%", condition.getActualValue());
    }

    @Test
    void testParseGreaterThanCondition() {
        Map<String, String[]> params = new HashMap<>();
        params.put("age_GT", new String[]{"18"});
        
        List<QueryCondition> conditions = parser.parseConditions(params);
        
        assertEquals(1, conditions.size());
        QueryCondition condition = conditions.get(0);
        assertEquals("age", condition.getFieldName());
        assertEquals(QueryOperator.GT, condition.getOperator());
        assertEquals(18L, condition.getValue());
    }

    @Test
    void testParseInCondition() {
        Map<String, String[]> params = new HashMap<>();
        params.put("status_IN", new String[]{"1,2,3"});
        
        List<QueryCondition> conditions = parser.parseConditions(params);
        
        assertEquals(1, conditions.size());
        QueryCondition condition = conditions.get(0);
        assertEquals("status", condition.getFieldName());
        assertEquals(QueryOperator.IN, condition.getOperator());
        assertEquals(3, condition.getValues().size());
        assertTrue(condition.getValues().contains(1L));
        assertTrue(condition.getValues().contains(2L));
        assertTrue(condition.getValues().contains(3L));
    }

    @Test
    void testParseIsNullCondition() {
        Map<String, String[]> params = new HashMap<>();
        params.put("deletedAt_IS_NULL", new String[]{"true"});
        
        List<QueryCondition> conditions = parser.parseConditions(params);
        
        assertEquals(1, conditions.size());
        QueryCondition condition = conditions.get(0);
        assertEquals("deletedAt", condition.getFieldName());
        assertEquals(QueryOperator.IS_NULL, condition.getOperator());
    }

    @Test
    void testParseMultipleConditions() {
        Map<String, String[]> params = new HashMap<>();
        params.put("name_LIKE", new String[]{"张"});
        params.put("age_GT", new String[]{"18"});
        params.put("status", new String[]{"1"});
        
        List<QueryCondition> conditions = parser.parseConditions(params);
        
        assertEquals(3, conditions.size());
    }

    @Test
    void testIgnoreEmptyValues() {
        Map<String, String[]> params = new HashMap<>();
        params.put("name", new String[]{""});
        params.put("age", new String[]{null});
        params.put("status", new String[]{"1"});
        
        List<QueryCondition> conditions = parser.parseConditions(params);
        
        assertEquals(1, conditions.size());
        assertEquals("status", conditions.get(0).getFieldName());
    }

    @Test
    void testIgnorePaginationParams() {
        Map<String, String[]> params = new HashMap<>();
        params.put("page", new String[]{"1"});
        params.put("size", new String[]{"20"});
        params.put("sort", new String[]{"name"});
        params.put("name", new String[]{"张三"});
        
        List<QueryCondition> conditions = parser.parseConditions(params);
        
        assertEquals(1, conditions.size());
        assertEquals("name", conditions.get(0).getFieldName());
    }

    @Test
    void testParseDateTimeValue() {
        Map<String, String[]> params = new HashMap<>();
        params.put("createTime_GT", new String[]{"2025-07-29 10:30:00"});
        
        List<QueryCondition> conditions = parser.parseConditions(params);
        
        assertEquals(1, conditions.size());
        QueryCondition condition = conditions.get(0);
        assertEquals("createTime", condition.getFieldName());
        assertEquals(QueryOperator.GT, condition.getOperator());
        assertNotNull(condition.getValue());
    }

    @Test
    void testParseBooleanValue() {
        Map<String, String[]> params = new HashMap<>();
        params.put("isPublic", new String[]{"true"});
        
        List<QueryCondition> conditions = parser.parseConditions(params);
        
        assertEquals(1, conditions.size());
        QueryCondition condition = conditions.get(0);
        assertEquals("isPublic", condition.getFieldName());
        assertEquals(QueryOperator.EQ, condition.getOperator());
        assertEquals(true, condition.getValue());
    }
}
