package com.hys.hm.shared.framework.repository;

import com.hys.hm.shared.framework.base.BaseEntity;
import com.hys.hm.shared.framework.query.QueryCondition;
import com.hys.hm.shared.framework.query.QueryOperator;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.boot.test.autoconfigure.orm.jpa.TestEntityManager;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * BaseRepositoryImpl 测试类
 * 验证修复后的代码是否正常工作
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-29
 */
@DataJpaTest
@SpringJUnitConfig
class BaseRepositoryImplTest {

    /**
     * 测试实体类
     */
    @Entity
    @Table(name = "test_entity")
    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class TestEntity extends BaseEntity<String> {
        
        @Id
        @Column(length = 32)
        private String id;
        
        @Column(length = 100, nullable = false)
        private String name;
        
        @Column
        private Integer age;
        
        @Column
        private Double score;
        
        @Override
        public String getId() {
            return id;
        }
        
        @Override
        public void setId(String id) {
            this.id = id;
        }
    }

    /**
     * 测试仓储接口
     */
    public interface TestEntityRepository extends BaseRepository<TestEntity, String> {
    }

    @Test
    void testQueryConditionTypes() {
        // 测试各种查询条件的类型转换是否正确
        
        // 等于条件
        QueryCondition eqCondition = QueryCondition.eq("name", "测试");
        assertTrue(eqCondition.isValid());
        assertEquals(QueryOperator.EQ, eqCondition.getOperator());
        
        // 大于条件
        QueryCondition gtCondition = QueryCondition.gt("age", 18);
        assertTrue(gtCondition.isValid());
        assertEquals(QueryOperator.GT, gtCondition.getOperator());
        
        // 模糊查询条件
        QueryCondition likeCondition = QueryCondition.like("name", "测试");
        assertTrue(likeCondition.isValid());
        assertEquals(QueryOperator.LIKE, likeCondition.getOperator());
        assertEquals("%测试%", likeCondition.getActualValue());
        
        // IN 条件
        QueryCondition inCondition = QueryCondition.in("age", List.of(18, 20, 25));
        assertTrue(inCondition.isValid());
        assertEquals(QueryOperator.IN, inCondition.getOperator());
        
        // BETWEEN 条件
        QueryCondition betweenCondition = QueryCondition.between("score", 80.0, 100.0);
        assertTrue(betweenCondition.isValid());
        assertEquals(QueryOperator.BETWEEN, betweenCondition.getOperator());
        
        // IS NULL 条件
        QueryCondition nullCondition = QueryCondition.isNull("remark");
        assertTrue(nullCondition.isValid());
        assertEquals(QueryOperator.IS_NULL, nullCondition.getOperator());
    }

    @Test
    void testQueryOperatorFromString() {
        // 测试操作符字符串解析
        assertEquals(QueryOperator.EQ, QueryOperator.fromString("EQ"));
        assertEquals(QueryOperator.LIKE, QueryOperator.fromString("LIKE"));
        assertEquals(QueryOperator.GT, QueryOperator.fromString("GT"));
        assertEquals(QueryOperator.LT, QueryOperator.fromString("LT"));
        assertEquals(QueryOperator.GTE, QueryOperator.fromString("GTE"));
        assertEquals(QueryOperator.LTE, QueryOperator.fromString("LTE"));
        assertEquals(QueryOperator.IN, QueryOperator.fromString("IN"));
        assertEquals(QueryOperator.NOT_IN, QueryOperator.fromString("NOT_IN"));
        assertEquals(QueryOperator.IS_NULL, QueryOperator.fromString("IS_NULL"));
        assertEquals(QueryOperator.IS_NOT_NULL, QueryOperator.fromString("IS_NOT_NULL"));
        assertEquals(QueryOperator.BETWEEN, QueryOperator.fromString("BETWEEN"));
        
        // 测试默认值
        assertEquals(QueryOperator.EQ, QueryOperator.fromString("UNKNOWN"));
        assertEquals(QueryOperator.EQ, QueryOperator.fromString(null));
        assertEquals(QueryOperator.EQ, QueryOperator.fromString(""));
    }

    @Test
    void testOperatorRequirements() {
        // 测试操作符的值要求
        assertTrue(QueryOperator.EQ.requiresValue());
        assertTrue(QueryOperator.LIKE.requiresValue());
        assertTrue(QueryOperator.GT.requiresValue());
        assertFalse(QueryOperator.IS_NULL.requiresValue());
        assertFalse(QueryOperator.IS_NOT_NULL.requiresValue());
        
        // 测试多值要求
        assertTrue(QueryOperator.IN.requiresMultipleValues());
        assertTrue(QueryOperator.NOT_IN.requiresMultipleValues());
        assertTrue(QueryOperator.BETWEEN.requiresMultipleValues());
        assertFalse(QueryOperator.EQ.requiresMultipleValues());
        assertFalse(QueryOperator.LIKE.requiresMultipleValues());
    }

    @Test
    void testOperatorSupport() {
        // 测试操作符支持的操作类型
        assertTrue(QueryOperator.LIKE.supportsStringOperation());
        assertTrue(QueryOperator.EQ.supportsStringOperation());
        assertFalse(QueryOperator.GT.supportsStringOperation());
        
        assertTrue(QueryOperator.GT.supportsNumericComparison());
        assertTrue(QueryOperator.BETWEEN.supportsNumericComparison());
        assertFalse(QueryOperator.LIKE.supportsNumericComparison());
        
        assertTrue(QueryOperator.GT.supportsDateComparison());
        assertTrue(QueryOperator.BETWEEN.supportsDateComparison());
        assertFalse(QueryOperator.LIKE.supportsDateComparison());
    }
}
