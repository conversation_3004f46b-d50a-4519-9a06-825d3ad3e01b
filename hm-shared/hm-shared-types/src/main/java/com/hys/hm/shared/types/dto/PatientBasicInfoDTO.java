package com.hys.hm.shared.types.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 患者基础信息DTO
 * 用于模块间数据传输的标准患者信息
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-29
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PatientBasicInfoDTO {
    
    /**
     * 患者ID
     */
    private String patientId;
    
    /**
     * 患者姓名
     */
    private String name;
    
    /**
     * 性别：1-男，2-女
     */
    private Integer gender;
    
    /**
     * 年龄
     */
    private Integer age;
    
    /**
     * 联系电话（脱敏）
     */
    private String phone;
    
    /**
     * 身份证号（脱敏）
     */
    private String idCard;
    
    /**
     * 地址
     */
    private String address;
    
    /**
     * 详细地址
     */
    private String addressDetail;
    
    /**
     * 出生日期
     */
    private LocalDate birthDate;
    
    /**
     * 档案编号
     */
    private String fileNumber;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 获取性别描述
     */
    public String getGenderDesc() {
        return gender != null && gender == 1 ? "男" : "女";
    }
    
    /**
     * 获取脱敏手机号
     */
    public String getMaskedPhone() {
        if (phone == null || phone.length() < 7) {
            return phone;
        }
        return phone.substring(0, 3) + "****" + phone.substring(7);
    }
    
    /**
     * 获取脱敏身份证号
     */
    public String getMaskedIdCard() {
        if (idCard == null || idCard.length() < 8) {
            return idCard;
        }
        return idCard.substring(0, 4) + "**********" + idCard.substring(idCard.length() - 4);
    }
}
