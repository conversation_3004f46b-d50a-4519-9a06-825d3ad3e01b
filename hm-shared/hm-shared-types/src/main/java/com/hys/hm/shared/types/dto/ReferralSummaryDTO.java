package com.hys.hm.shared.types.dto;

import com.hys.hm.shared.types.enums.ReferralStatus;
import com.hys.hm.shared.types.enums.UrgencyLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 转诊摘要DTO
 * 用于模块间数据传输的转诊摘要信息
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-29
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReferralSummaryDTO {
    
    /**
     * 转诊ID
     */
    private String referralId;
    
    /**
     * 转诊编号
     */
    private String referralNo;
    
    /**
     * 患者ID
     */
    private String patientId;
    
    /**
     * 患者姓名
     */
    private String patientName;
    
    /**
     * 转出医院ID
     */
    private String outUnitId;
    
    /**
     * 转出医院名称
     */
    private String outUnitName;
    
    /**
     * 转入医院ID
     */
    private String inUnitId;
    
    /**
     * 转入医院名称
     */
    private String inUnitName;
    
    /**
     * 转诊状态
     */
    private ReferralStatus status;
    
    /**
     * 转诊日期
     */
    private LocalDateTime referralDate;
    
    /**
     * 紧急程度
     */
    private UrgencyLevel urgencyLevel;
    
    /**
     * 转诊原因（简要）
     */
    private String referralReason;
    
    /**
     * 确认时间
     */
    private LocalDateTime confirmTime;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 获取状态描述
     */
    public String getStatusDesc() {
        return status != null ? status.getDescription() : "未知";
    }
    
    /**
     * 获取紧急程度描述
     */
    public String getUrgencyDesc() {
        return urgencyLevel != null ? urgencyLevel.getDescription() : "普通";
    }
    
    /**
     * 判断是否为待处理状态
     */
    public boolean isPending() {
        return ReferralStatus.PENDING.equals(status);
    }
    
    /**
     * 判断是否为已确认状态
     */
    public boolean isConfirmed() {
        return ReferralStatus.CONFIRMED.equals(status);
    }
    
    /**
     * 判断是否为紧急转诊
     */
    public boolean isUrgent() {
        return urgencyLevel != null && urgencyLevel.getLevel() >= 2;
    }
}
