package com.hys.hm.shared.types.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 转诊状态枚举
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-29
 */
@Getter
@AllArgsConstructor
public enum ReferralStatus {
    
    /**
     * 待处理
     */
    PENDING(1, "待处理"),
    
    /**
     * 已确认
     */
    CONFIRMED(2, "已确认"),
    
    /**
     * 已拒绝
     */
    REJECTED(3, "已拒绝"),
    
    /**
     * 已取消
     */
    CANCELLED(4, "已取消"),
    
    /**
     * 已完成
     */
    COMPLETED(5, "已完成");
    
    /**
     * 状态码
     */
    private final Integer code;
    
    /**
     * 状态描述
     */
    private final String description;
    
    /**
     * 根据状态码获取枚举
     */
    public static ReferralStatus fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        
        for (ReferralStatus status : values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        
        throw new IllegalArgumentException("未知的转诊状态码: " + code);
    }
    
    /**
     * 判断是否为终态
     */
    public boolean isFinalStatus() {
        return this == REJECTED || this == CANCELLED || this == COMPLETED;
    }
    
    /**
     * 判断是否可以确认
     */
    public boolean canConfirm() {
        return this == PENDING;
    }
    
    /**
     * 判断是否可以拒绝
     */
    public boolean canReject() {
        return this == PENDING;
    }
    
    /**
     * 判断是否可以取消
     */
    public boolean canCancel() {
        return this == PENDING || this == CONFIRMED;
    }
    
    /**
     * 判断是否可以完成
     */
    public boolean canComplete() {
        return this == CONFIRMED;
    }
}
