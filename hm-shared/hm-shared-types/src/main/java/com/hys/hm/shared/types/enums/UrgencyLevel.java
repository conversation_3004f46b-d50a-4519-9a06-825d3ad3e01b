package com.hys.hm.shared.types.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 紧急程度枚举
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-29
 */
@Getter
@AllArgsConstructor
public enum UrgencyLevel {
    
    /**
     * 普通
     */
    NORMAL(1, "普通"),
    
    /**
     * 紧急
     */
    URGENT(2, "紧急"),
    
    /**
     * 急诊
     */
    EMERGENCY(3, "急诊");
    
    /**
     * 级别
     */
    private final Integer level;
    
    /**
     * 描述
     */
    private final String description;
    
    /**
     * 根据级别获取枚举
     */
    public static UrgencyLevel fromLevel(Integer level) {
        if (level == null) {
            return NORMAL;
        }
        
        for (UrgencyLevel urgency : values()) {
            if (urgency.level.equals(level)) {
                return urgency;
            }
        }
        
        return NORMAL;
    }
    
    /**
     * 判断是否为紧急
     */
    public boolean isUrgent() {
        return this.level >= 2;
    }
    
    /**
     * 判断是否为急诊
     */
    public boolean isEmergency() {
        return this == EMERGENCY;
    }
}
