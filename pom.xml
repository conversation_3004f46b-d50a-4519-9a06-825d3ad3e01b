<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.5.3</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>

    <groupId>com.hys</groupId>
    <artifactId>hm</artifactId>
    <version>3.0.1-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>hm</name>
    <description>健康管理系统 - 多模块父项目</description>

    <properties>
        <!-- 项目版本管理 -->
        <revision>3.0.1-SNAPSHOT</revision>

        <!-- Java版本 -->
        <java.version>21</java.version>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

        <!-- 第三方依赖版本 -->
        <common-lang3.version>3.18.0</common-lang3.version>
        <aviator.version>5.3.3</aviator.version>
        <javassist.version>3.29.2-GA</javassist.version>
        <commons-beanutils.version>1.9.4</commons-beanutils.version>
        <swagger-annotations-jakarta.version>2.2.27</swagger-annotations-jakarta.version>
        <jakarta.xml.bind-api.version>4.0.0</jakarta.xml.bind-api.version>
        <lombok.version>1.18.38</lombok.version>
    </properties>

    <!-- 子模块定义 -->
    <modules>
        <module>hm-shared</module>
        <module>hm-domain</module>
        <module>hm-infrastructure</module>
        <module>hm-application</module>
        <module>hm-interfaces</module>
        <module>hm-bootstrap</module>
    </modules>

    <!-- 依赖管理 -->
    <dependencyManagement>
        <dependencies>
            <!-- 内部模块依赖 -->
            <dependency>
                <groupId>com.hys</groupId>
                <artifactId>hm-shared-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hys</groupId>
                <artifactId>hm-shared-types</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hys</groupId>
                <artifactId>hm-shared-framework</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hys</groupId>
                <artifactId>hm-shared-local</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hys</groupId>
                <artifactId>hm-shared-logging</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hys</groupId>
                <artifactId>hm-domain-patient</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hys</groupId>
                <artifactId>hm-domain-health</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hys</groupId>
                <artifactId>hm-domain-communication</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hys</groupId>
                <artifactId>hm-domain-followup</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hys</groupId>
                <artifactId>hm-domain-knowledge</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hys</groupId>
                <artifactId>hm-domain-referral</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hys</groupId>
                <artifactId>hm-domain-system</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hys</groupId>
                <artifactId>hm-application-referral</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hys</groupId>
                <artifactId>hm-infrastructure-persistence</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hys</groupId>
                <artifactId>hm-infrastructure-persistence-referral</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hys</groupId>
                <artifactId>hm-infrastructure-ai</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hys</groupId>
                <artifactId>hm-infrastructure-external</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hys</groupId>
                <artifactId>hm-infrastructure-logging</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hys</groupId>
                <artifactId>hm-application-patient</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hys</groupId>
                <artifactId>hm-application-health</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hys</groupId>
                <artifactId>hm-interfaces-web</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- 第三方依赖版本管理 -->
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
                <version>2.2.0</version>
            </dependency>
            <!-- Apache Commons Lang3 -->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${common-lang3.version}</version>
            </dependency>
            <!-- Aviator 脚本引擎 -->
            <dependency>
                <groupId>com.googlecode.aviator</groupId>
                <artifactId>aviator</artifactId>
                <version>${aviator.version}</version>
            </dependency>
            <!-- javassist -->
            <dependency>
                <groupId>org.javassist</groupId>
                <artifactId>javassist</artifactId>
                <version>${javassist.version}</version>
            </dependency>
            <!-- commons-beanutils -->
            <dependency>
                <groupId>commons-beanutils</groupId>
                <artifactId>commons-beanutils</artifactId>
                <version>${commons-beanutils.version}</version>
            </dependency>
            <!-- swagger-annotations-jakarta -->
            <dependency>
                <groupId>io.swagger.core.v3</groupId>
                <artifactId>swagger-annotations-jakarta</artifactId>
                <version>${swagger-annotations-jakarta.version}</version>
            </dependency>

            <!-- jaxb-api -->
            <dependency>
                <groupId>jakarta.xml.bind</groupId>
                <artifactId>jakarta.xml.bind-api</artifactId>
                <version>${jakarta.xml.bind-api.version}</version>
            </dependency>

            <!-- lombok -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
                <scope>provided</scope>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <configuration>
                        <source>21</source>
                        <target>21</target>
                        <annotationProcessorPaths>
                            <path>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok</artifactId>
                                <version>${lombok.version}</version>
                            </path>
                        </annotationProcessorPaths>
                    </configuration>
                </plugin>
                <!-- 版本号管理插件 -->
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>flatten-maven-plugin</artifactId>
                    <version>1.5.0</version>
                    <configuration>
                        <updatePomFile>true</updatePomFile>
                        <flattenMode>resolveCiFriendliesOnly</flattenMode>
                    </configuration>
                    <executions>
                        <execution>
                            <id>flatten</id>
                            <phase>process-resources</phase>
                            <goals>
                                <goal>flatten</goal>
                            </goals>
                        </execution>
                        <execution>
                            <id>flatten.clean</id>
                            <phase>clean</phase>
                            <goals>
                                <goal>clean</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>

        <!-- 全局插件配置 -->
        <plugins>
            <!-- 版本号管理插件 -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>
